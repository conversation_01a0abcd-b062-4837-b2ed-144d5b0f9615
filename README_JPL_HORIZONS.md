# JPL HORIZONS 太阳系行星数据获取器

## 概述

本项目使用NASA JPL HORIZONS系统获取高精度的太阳系行星轨道数据。JPL HORIZONS是当今世界上最权威、最严谨的天体位置计算系统，被全球天文学家和航天机构广泛使用。

## 特点

- **权威性**: 使用NASA官方JPL HORIZONS系统
- **高精度**: 基于最新的行星理论和观测数据
- **完整性**: 包含位置、速度、距离等完整轨道信息
- **易用性**: 简单的Python接口，自动导出CSV格式

## 安装依赖

```bash
pip install -r requirements.txt
```

主要依赖库：
- `astroquery`: 用于查询JPL HORIZONS系统
- `astropy`: 天文学计算库
- `pandas`: 数据处理
- `numpy`: 数值计算
- `matplotlib`: 数据可视化

## 使用方法

### 1. 基本使用

```python
from jpl_horizons_fetcher import JPLHorizonsDataFetcher

# 创建数据获取器
fetcher = JPLHorizonsDataFetcher()

# 获取地球2023年的数据
earth_data = fetcher.get_planet_data('地球', 2023)

# 保存为CSV文件
fetcher.save_to_csv(earth_data, '地球', 2023)
```

### 2. 获取所有行星数据

```python
# 获取2023年所有主要行星的数据
fetcher.get_all_planets_data(2023)
```

### 3. 运行完整示例

```bash
python jpl_horizons_fetcher.py
```

或运行详细示例：

```bash
python example_usage.py
```

## 支持的天体

- 水星 (Mercury)
- 金星 (Venus)
- 地球 (Earth)
- 火星 (Mars)
- 木星 (Jupiter)
- 土星 (Saturn)
- 天王星 (Uranus)
- 海王星 (Neptune)
- 冥王星 (Pluto)
- 月球 (Moon)
- 太阳 (Sun)

## 数据格式

CSV文件包含以下列：

| 列名 | 描述 | 单位 |
|------|------|------|
| julian_date | 儒略日 | 天 |
| date | 日期字符串 | YYYY-MM-DD |
| x_position_au | X坐标位置 | AU |
| y_position_au | Y坐标位置 | AU |
| z_position_au | Z坐标位置 | AU |
| x_velocity_au_day | X方向速度 | AU/天 |
| y_velocity_au_day | Y方向速度 | AU/天 |
| z_velocity_au_day | Z方向速度 | AU/天 |
| distance_au | 到太阳距离 | AU |
| speed_km_s | 轨道速度 | km/s |

## 坐标系说明

- **参考系**: 日心黄道坐标系（太阳系质心参考系）
- **历元**: J2000.0
- **X轴**: 指向春分点
- **Y轴**: 在黄道面内，垂直于X轴
- **Z轴**: 垂直于黄道面，指向北黄极

## 数据精度

JPL HORIZONS系统提供的数据精度：
- **位置精度**: 优于1公里（对于主要行星）
- **速度精度**: 优于1米/秒
- **时间范围**: 公元前9999年到公元9999年

## 文件结构

```
├── jpl_horizons_fetcher.py    # 主要数据获取脚本
├── example_usage.py           # 使用示例
├── requirements.txt           # 依赖库列表
├── README_JPL_HORIZONS.md     # 说明文档
└── planet_data/               # 输出数据目录
    ├── 地球_2023_data.csv
    ├── 火星_2023_data.csv
    └── ...
```

## 注意事项

1. **网络连接**: 需要稳定的互联网连接访问JPL HORIZONS系统
2. **查询限制**: 大量查询可能需要时间，请耐心等待
3. **数据量**: 一年的每日数据约365行，文件大小适中
4. **时区**: 所有时间均为UTC时间

## 错误处理

如果遇到网络错误或查询失败，脚本会：
- 显示详细的错误信息
- 跳过失败的查询，继续处理其他行星
- 保存已成功获取的数据

## 扩展功能

可以通过修改脚本实现：
- 获取更高时间分辨率的数据（小时、分钟级别）
- 添加更多天体（小行星、彗星等）
- 不同坐标系的数据（地心坐标系等）
- 包含更多物理参数（视星等、相位角等）

## 参考资料

- [JPL HORIZONS官方网站](https://ssd.jpl.nasa.gov/horizons/)
- [astroquery文档](https://astroquery.readthedocs.io/)
- [astropy文档](https://docs.astropy.org/)
