#!/usr/bin/env python3
"""
JPL HORIZONS数据获取器使用示例

展示如何使用jpl_horizons_fetcher.py获取和分析行星数据
"""

from jpl_horizons_fetcher import JPLHorizonsDataFetcher
import pandas as pd
import matplotlib.pyplot as plt
import numpy as np

def example_single_planet():
    """示例：获取单个行星的数据"""
    print("示例1：获取地球2023年的轨道数据")
    print("=" * 40)
    
    fetcher = JPLHorizonsDataFetcher()
    
    # 获取地球2023年的数据
    earth_data = fetcher.get_planet_data('地球', 2023)
    
    if earth_data is not None:
        # 保存数据
        fetcher.save_to_csv(earth_data, '地球', 2023)
        
        # 显示前几行数据
        print("\n前5天的数据:")
        print(earth_data.head())
        
        # 简单的数据分析
        print(f"\n地球轨道分析:")
        print(f"平均日心距离: {earth_data['distance_au'].mean():.6f} AU")
        print(f"近日点距离: {earth_data['distance_au'].min():.6f} AU")
        print(f"远日点距离: {earth_data['distance_au'].max():.6f} AU")
        print(f"轨道偏心率估算: {(earth_data['distance_au'].max() - earth_data['distance_au'].min()) / (earth_data['distance_au'].max() + earth_data['distance_au'].min()):.6f}")

def example_multiple_planets():
    """示例：获取多个行星的数据并比较"""
    print("\n示例2：获取内行星数据并比较")
    print("=" * 40)
    
    fetcher = JPLHorizonsDataFetcher()
    
    # 获取内行星数据
    inner_planets = ['水星', '金星', '地球', '火星']
    planet_data = {}
    
    for planet in inner_planets:
        print(f"\n获取{planet}的数据...")
        data = fetcher.get_planet_data(planet, 2023)
        if data is not None:
            planet_data[planet] = data
            fetcher.save_to_csv(data, planet, 2023)
    
    # 比较分析
    if planet_data:
        print("\n内行星轨道参数比较:")
        print("-" * 60)
        print(f"{'行星':<8} {'平均距离(AU)':<12} {'最大速度(km/s)':<15} {'最小速度(km/s)':<15}")
        print("-" * 60)
        
        for planet, data in planet_data.items():
            avg_dist = data['distance_au'].mean()
            max_speed = data['speed_km_s'].max()
            min_speed = data['speed_km_s'].min()
            print(f"{planet:<8} {avg_dist:<12.6f} {max_speed:<15.2f} {min_speed:<15.2f}")

def example_custom_year():
    """示例：获取指定年份的数据"""
    print("\n示例3：获取指定年份的数据")
    print("=" * 40)
    
    fetcher = JPLHorizonsDataFetcher()
    
    # 获取2024年木星的数据
    jupiter_data = fetcher.get_planet_data('木星', 2024)
    
    if jupiter_data is not None:
        fetcher.save_to_csv(jupiter_data, '木星', 2024)
        
        print(f"\n木星2024年轨道数据:")
        print(f"数据点数: {len(jupiter_data)}")
        print(f"平均日心距离: {jupiter_data['distance_au'].mean():.6f} AU")
        print(f"平均轨道速度: {jupiter_data['speed_km_s'].mean():.2f} km/s")

def example_data_analysis():
    """示例：数据分析和可视化"""
    print("\n示例4：数据分析和可视化")
    print("=" * 40)
    
    fetcher = JPLHorizonsDataFetcher()
    
    # 获取地球数据
    earth_data = fetcher.get_planet_data('地球', 2023)
    
    if earth_data is not None:
        # 创建图表
        fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(15, 10))
        
        # 1. 轨道轨迹（XY平面）
        ax1.plot(earth_data['x_position_au'], earth_data['y_position_au'])
        ax1.scatter(0, 0, color='yellow', s=100, label='太阳')
        ax1.set_xlabel('X (AU)')
        ax1.set_ylabel('Y (AU)')
        ax1.set_title('地球轨道轨迹 (XY平面)')
        ax1.grid(True)
        ax1.axis('equal')
        ax1.legend()
        
        # 2. 日心距离随时间变化
        days = range(len(earth_data))
        ax2.plot(days, earth_data['distance_au'])
        ax2.set_xlabel('天数')
        ax2.set_ylabel('日心距离 (AU)')
        ax2.set_title('地球日心距离变化')
        ax2.grid(True)
        
        # 3. 轨道速度随时间变化
        ax3.plot(days, earth_data['speed_km_s'])
        ax3.set_xlabel('天数')
        ax3.set_ylabel('轨道速度 (km/s)')
        ax3.set_title('地球轨道速度变化')
        ax3.grid(True)
        
        # 4. 速度-距离关系
        ax4.scatter(earth_data['distance_au'], earth_data['speed_km_s'], alpha=0.6)
        ax4.set_xlabel('日心距离 (AU)')
        ax4.set_ylabel('轨道速度 (km/s)')
        ax4.set_title('速度-距离关系')
        ax4.grid(True)
        
        plt.tight_layout()
        plt.savefig('planet_data/earth_2023_analysis.png', dpi=300, bbox_inches='tight')
        plt.show()
        
        print("分析图表已保存为: planet_data/earth_2023_analysis.png")

def main():
    """主函数 - 运行所有示例"""
    print("JPL HORIZONS数据获取器使用示例")
    print("=" * 50)
    
    try:
        # 运行示例
        example_single_planet()
        example_multiple_planets()
        example_custom_year()
        example_data_analysis()
        
        print("\n所有示例运行完成！")
        print("请查看 planet_data/ 目录中的CSV文件和图表")
        
    except Exception as e:
        print(f"运行示例时发生错误: {e}")
        print("请确保已安装所需的依赖库：")
        print("pip install astroquery astropy pandas matplotlib")

if __name__ == "__main__":
    main()
