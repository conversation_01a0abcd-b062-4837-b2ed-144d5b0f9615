import numpy as np
from astropy.time import Time
from astropy import units as u
from astropy.coordinates import solar_system_ephemeris, get_body_barycentric
import matplotlib.pyplot as plt
from mpl_toolkits.mplot3d import Axes3D
import tkinter as tk
from tkinter import ttk
from matplotlib.backends.backend_tkagg import FigureCanvasTkAgg

class OrbitalCalculator:
    def __init__(self, root):
        self.root = root
        self.root.title("太阳系轨道计算器")
        
        # 设置初始窗口大小和最小尺寸
        self.root.geometry("1200x800")
        self.root.minsize(800, 600)

        # 创建主布局
        main_frame = ttk.Frame(root)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        # 左侧控制面板
        left_frame = ttk.Frame(main_frame)
        left_frame.pack(side=tk.LEFT, fill=tk.Y, padx=5)

        # 创建输入区域
        self.create_input_fields(left_frame)
        
        # 创建行星按钮区域
        self.create_planet_buttons(left_frame)

        # 中间显示区域
        middle_frame = ttk.Frame(main_frame)
        middle_frame.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=5)

        # 右侧计算过程说明区域
        right_frame = ttk.Frame(main_frame)
        right_frame.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=5)

        # 创建显示区域
        self.create_plot_area(middle_frame)
        
        # 创建计算过程说明区域
        self.create_calculation_area(right_frame)

        # 创建速度-距离关系图表区域
        self.create_velocity_chart_area(right_frame)

    def create_input_fields(self, parent):
        """创建输入区域"""
        # 设置更大的字体
        large_font = ('Microsoft YaHei', 14)  # 使用微软雅黑，更大字号
        
        # 创建标签和输入框
        labels = [
            "半长轴 (AU):",
            "偏心率:",
            "轨道倾角 (度):",
            "平均经度 (度):",
            "近日点经度 (度):",
            "升交点黄经 (度):"
        ]
        
        # 默认值（地球轨道参数）
        default_values = [
            "1.00000011",
            "0.01671022",
            "0.00005",
            "100.46435",
            "102.94719",
            "-11.26064"
        ]
        
        # 创建输入框容器
        input_frame = ttk.LabelFrame(parent, text="轨道参数")
        input_frame.pack(fill=tk.X, padx=5, pady=5)
        
        # 设置标签框架的字体
        style = ttk.Style()
        style.configure('TLabelframe.Label', font=large_font)
        
        self.entries = {}
        for i, (label, default) in enumerate(zip(labels, default_values)):
            frame = ttk.Frame(input_frame)
            frame.pack(fill=tk.X, padx=5, pady=2)
            
            label_widget = ttk.Label(frame, text=label, width=15, font=large_font)
            label_widget.pack(side=tk.LEFT)
            
            entry = ttk.Entry(frame, width=15, font=large_font)
            entry.insert(0, default)
            entry.pack(side=tk.LEFT, padx=5)
            self.entries[label] = entry
        
        # 添加时间输入框
        time_frame = ttk.Frame(input_frame)
        time_frame.pack(fill=tk.X, padx=5, pady=2)
        
        time_label = ttk.Label(time_frame, text="时间 (YYYY-MM-DD):", width=15, font=large_font)
        time_label.pack(side=tk.LEFT)
        
        self.time_entry = ttk.Entry(time_frame, width=15, font=large_font)
        current_time = Time.now().datetime.strftime('%Y-%m-%d')
        self.time_entry.insert(0, current_time)
        self.time_entry.pack(side=tk.LEFT, padx=5)
        
        # 创建按钮容器
        button_frame = ttk.Frame(parent)
        button_frame.pack(pady=5)
        
        # 更新轨道按钮
        self.update_button = ttk.Button(button_frame, text="更新轨道", command=self.update_orbit)
        style.configure('TButton', font=large_font)
        self.update_button.pack(side=tk.LEFT, padx=5)
        
        # 计算速度按钮
        self.calc_speed_button = ttk.Button(button_frame, text="计算速度", command=self.calculate_speed)
        self.calc_speed_button.pack(side=tk.LEFT, padx=5)
        
        # 计算每日数据按钮
        self.daily_data_button = ttk.Button(button_frame, text="计算每日数据", 
                                     command=self.calculate_daily_values)
        self.daily_data_button.pack(side=tk.LEFT, padx=5)

    def create_planet_buttons(self, parent):
        """创建行星按钮区域"""
        large_font = ('Microsoft YaHei', 14)
        
        # 行星参数（依次为：半长轴(AU)、偏心率、轨道倾角(度)、平均经度(度)、近日点经度(度)、升交点黄经(度)）
        self.planet_params = {
            "水星": [0.38709927,0.205636,7.004979,252.2503235,77.45779628,48.33076593],
            "金星": [0.72333566,0.006777,3.394676,181.9790995,131.6024672,76.67984255],
            "地球": [1.00000261,0.016711,-0.000015,100.4645717,102.9376819,0],
            "火星": [1.52371034,0.093394,1.849691,-4.55343205,-23.94362959,49.55953891],
            "木星": [5.202887,0.048386,1.304397,34.39644051,14.72847983,100.4739091],
            "土星": [9.53667594,0.053862,2.485992,49.95424423,92.59887831,113.6624245],
            "天王星": [19.18916464,0.047257,0.772638,313.2381045,170.9542763,74.01692503],
            "海王星": [30.06992276,0.00859,1.770043,-55.1200297,44.96476227,131.7842257],
            "冥王星": [39.54,0.24905,17.1405,110.30347,113.834,110.299]
        }

        # 创建按钮容器
        button_frame = ttk.LabelFrame(parent, text="太阳系天体", padding="5 5 5 5")
        button_frame.pack(fill=tk.X, padx=5, pady=5)

        # 设置标签框架的字体
        style = ttk.Style()
        style.configure('TLabelframe.Label', font=large_font)

        # 创建行星按钮
        for planet in self.planet_params:
            button = ttk.Button(button_frame, text=planet, 
                              command=lambda p=planet: self.set_planet_params(p))
            button.pack(fill=tk.X, padx=5, pady=2)
            style.configure('TButton', font=large_font)

    def set_planet_params(self, planet):
        """设置选定行星的轨道参数"""
        params = self.planet_params[planet]
        labels = [
            "半长轴 (AU):",
            "偏心率:",
            "轨道倾角 (度):",
            "平均经度 (度):",
            "近日点经度 (度):",
            "升交点黄经 (度):"
        ]
        
        # 更新输入框的值
        for label, value in zip(labels, params):
            self.entries[label].delete(0, tk.END)
            self.entries[label].insert(0, str(value))
        
        # 自动更新轨道
        self.update_orbit()
        
        # 自动计算并显示该行星的每日数据
        self.calculate_daily_values(planet)

    def create_plot_area(self, parent):
        """创建绘图区域"""
        plot_frame = ttk.LabelFrame(parent, text="轨道可视化")
        plot_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        self.fig = plt.figure(figsize=(8, 8))
        self.ax = self.fig.add_subplot(111, projection='3d')
        self.canvas = FigureCanvasTkAgg(self.fig, master=plot_frame)
        self.canvas.get_tk_widget().pack(fill=tk.BOTH, expand=True)

    def create_calculation_area(self, parent):
        """创建计算过程说明区域"""
        # 设置更大的字体
        large_font = ('Microsoft YaHei', 14)  # 使用微软雅黑，更大字号
        
        calc_frame = ttk.LabelFrame(parent, text="计算过程", width=400)
        calc_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        # 使用Text控件显示计算过程
        self.calc_text = tk.Text(calc_frame, wrap=tk.WORD, width=45, height=30, font=large_font)
        self.calc_text.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        # 添加滚动条
        scrollbar = ttk.Scrollbar(calc_frame, orient=tk.VERTICAL, command=self.calc_text.yview)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        self.calc_text.configure(yscrollcommand=scrollbar.set)
        
        # 设置只读
        self.calc_text.configure(state='disabled')

    def create_velocity_chart_area(self, parent):
        """创建速度-距离关系图表区域"""
        velocity_chart_frame = ttk.LabelFrame(parent, text="速度-距离关系图表")
        velocity_chart_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        self.velocity_fig, self.velocity_ax = plt.subplots(figsize=(6, 4))
        self.velocity_canvas = FigureCanvasTkAgg(self.velocity_fig, master=velocity_chart_frame)
        self.velocity_canvas.get_tk_widget().pack(fill=tk.BOTH, expand=True)

    def solve_kepler(self, M, e, tolerance=1e-8):
        """求解开普勒方程"""
        E = M  # 初始猜测值
        while True:
            E_next = M + e * np.sin(E)
            if abs(E_next - E) < tolerance:
                return E
            E = E_next

    def get_position(self, a, e, i, L, omega, Omega, time=None):
        """
        计算特定时间的位置
        time: astropy Time对象，如果为None则使用当前时间
        返回：在日心黄道坐标系中的位置（x, y, z，单位：AU）
        """
        # 如果提供了时间，计算该时间点的平近点角
        if time is not None:
            # 计算参考时间（J2000.0）到目标时间的年数
            t_ref = Time('2000-01-01T12:00:00')
            years_since_ref = (time - t_ref).jd / 365.25
            
            # 计算平均角速度（弧度/年）
            n = 2 * np.pi / (a ** 1.5)  # 开普勒第三定律
            
            # 计算目标时间的平近点角
            M = (L - omega + n * years_since_ref) % (2 * np.pi)
        else:
            # 如果没有提供时间，使用默认的平近点角
            M = L - omega
        
        # 求解开普勒方程得到偏近点角
        E = self.solve_kepler(M, e)
        
        # 计算真近点角
        nu = 2 * np.arctan(np.sqrt((1 + e)/(1 - e)) * np.tan(E/2))
        
        # 计算在轨道平面内的位置（焦点坐标系）
        r = a * (1 - e * np.cos(E))
        x_orbit = r * np.cos(nu)
        y_orbit = r * np.sin(nu)
        
        # 进行坐标转换到黄道坐标系
        Rz_omega = np.array([
            [np.cos(-omega), -np.sin(-omega), 0],
            [np.sin(-omega), np.cos(-omega), 0],
            [0, 0, 1]
        ])
        
        Rx_i = np.array([
            [1, 0, 0],
            [0, np.cos(-i), -np.sin(-i)],
            [0, np.sin(-i), np.cos(-i)]
        ])
        
        Rz_Omega = np.array([
            [np.cos(-Omega), -np.sin(-Omega), 0],
            [np.sin(-Omega), np.cos(-Omega), 0],
            [0, 0, 1]
        ])
        
        pos_orbit = np.array([x_orbit, y_orbit, 0])
        pos = Rz_Omega @ Rx_i @ Rz_omega @ pos_orbit
        
        return pos

    def plot_orbit(self, a, e, i, L, omega, Omega):
        """绘制轨道"""
        self.ax.clear()
        
        # 绘制黄道面（浅黄色半透明圆盘）
        r_ecliptic = max(a * (1 + e) * 1.2, 2)  # 黄道面半径略大于轨道
        theta = np.linspace(0, 2*np.pi, 100)
        x_ecliptic = r_ecliptic * np.cos(theta)
        y_ecliptic = r_ecliptic * np.sin(theta)
        z_ecliptic = np.zeros_like(theta)
        
        # 创建网格并绘制黄道面
        self.ax.plot_surface(
            x_ecliptic.reshape(10,10), 
            y_ecliptic.reshape(10,10), 
            z_ecliptic.reshape(10,10),
            color='yellow', alpha=0.1
        )
        
        # 添加四季标记
        seasons = {
            "春分 (0°)": (r_ecliptic, 0, 0),
            "夏至 (90°)": (0, r_ecliptic, 0),
            "秋分 (180°)": (-r_ecliptic, 0, 0),
            "冬至 (270°)": (0, -r_ecliptic, 0)
        }
        
        for season, pos in seasons.items():
            self.ax.text(pos[0], pos[1], 0, season, 
                        fontsize=10, fontfamily='Microsoft YaHei',
                        horizontalalignment='center', verticalalignment='center')
        
        # 生成一系列的真近点角
        nus = np.linspace(0, 2*np.pi, 100)
        xs, ys, zs = [], [], []
        
        # 计算远日点的位置（真近点角 = 180度）
        nu_aphelion = np.pi
        r_aphelion = a * (1 - e**2) / (1 + e * np.cos(nu_aphelion))
        x_orbit_aphelion = r_aphelion * np.cos(nu_aphelion)
        y_orbit_aphelion = r_aphelion * np.sin(nu_aphelion)
        pos_orbit_aphelion = np.array([x_orbit_aphelion, y_orbit_aphelion, 0])
        
        # 坐标变换矩阵
        Rz_omega = np.array([
            [np.cos(-omega), -np.sin(-omega), 0],
            [np.sin(-omega), np.cos(-omega), 0],
            [0, 0, 1]
        ])
        
        Rx_i = np.array([
            [1, 0, 0],
            [0, np.cos(-i), -np.sin(-i)],
            [0, np.sin(-i), np.cos(-i)]
        ])
        
        Rz_Omega = np.array([
            [np.cos(-Omega), -np.sin(-Omega), 0],
            [np.sin(-Omega), np.cos(-Omega), 0],
            [0, 0, 1]
        ])
        
        # 计算远日点的最终位置
        aphelion_pos = Rz_Omega @ Rx_i @ Rz_omega @ pos_orbit_aphelion
        
        for nu in nus:
            # 计算径向距离
            r = a * (1 - e**2) / (1 + e * np.cos(nu))
            
            # 计算在轨道平面内的位置
            x_orbit = r * np.cos(nu)
            y_orbit = r * np.sin(nu)
            pos_orbit = np.array([x_orbit, y_orbit, 0])
            
            # 应用坐标变换
            pos = Rz_Omega @ Rx_i @ Rz_omega @ pos_orbit
            
            xs.append(pos[0])
            ys.append(pos[1])
            zs.append(pos[2])
        
        # 绘制轨道
        self.ax.plot(xs, ys, zs)
        
        # 绘制太阳位置
        self.ax.scatter([0], [0], [0], color='yellow', s=100, label='Sun')
        
        # 标记远日点位置
        self.ax.scatter(aphelion_pos[0], aphelion_pos[1], aphelion_pos[2], 
                       color='red', s=100, label='Aphelion')
        
        # 设置图形属性
        self.ax.set_xlabel('X (AU)')
        self.ax.set_ylabel('Y (AU)')
        self.ax.set_zlabel('Z (AU)')
        self.ax.set_title('Orbital Path')
        self.ax.legend()
        
        # 确保三个坐标轴使用相同的比例
        max_range = np.array([
            max(xs) - min(xs),
            max(ys) - min(ys),
            max(zs) - min(zs)
        ]).max() * 0.5
        
        mid_x = (max(xs) + min(xs)) * 0.5
        mid_y = (max(ys) + min(ys)) * 0.5
        mid_z = (max(zs) + min(zs)) * 0.5
        
        self.ax.set_xlim(mid_x - max_range, mid_x + max_range)
        self.ax.set_ylim(mid_y - max_range, mid_y + max_range)
        self.ax.set_zlim(mid_z - max_range, mid_z + max_range)
        
        # 设置等比例
        self.ax.set_box_aspect([1,1,1])
        
        self.canvas.draw()

    def update_calculation_text(self, a, e, i, L, omega, Omega):
        """更新计算过程说明"""
        self.calc_text.configure(state='normal')
        self.calc_text.delete(1.0, tk.END)
        
        # 添加计算过程说明
        text = "轨道计算过程：\n\n"
        
        # 1. 输入参数说明
        text += "1. 轨道根数（J2000历元）：\n"
        text += f"  - 半长轴 (a) = {a:.6f} AU\n"
        text += f"  - 偏心率 (e) = {e:.6f}\n"
        text += f"  - 轨道倾角 (i) = {i:.6f}°\n"
        text += f"  - 平均经度 (L) = {L:.6f}°\n"
        text += f"  - 近日点经度 (ω) = {omega:.6f}°\n"
        text += f"  - 升交点黄经 (Ω) = {Omega:.6f}°\n\n"
        
        # 2. 计算平近点角
        M = L - omega
        text += "2. 计算平近点角 (M)：\n"
        text += "   M = L - ω\n"
        text += f"   M = {M:.6f}°\n\n"
        
        # 3. 求解开普勒方程
        E = self.solve_kepler(M, e)
        text += "3. 求解开普勒方程：\n"
        text += "   E = M + e·sin(E)\n"
        text += f"   E = {E:.6f}°\n\n"
        
        # 4. 计算真近点角
        nu = 2 * np.arctan(np.sqrt((1 + e)/(1 - e)) * np.tan(E/2))
        text += "4. 计算真近点角 (ν)：\n"
        text += "   tan(ν/2) = √((1+e)/(1-e))·tan(E/2)\n"
        text += f"   ν = {nu:.6f}°\n\n"
        
        # 5. 计算日心距离
        r = a * (1 - e * np.cos(E))
        text += "5. 计算日心距离 (r)：\n"
        text += "   r = a·(1 - e·cos(E))\n"
        text += f"   r = {r:.6f} AU\n\n"
        
        # 6. 计算轨道平面坐标
        x_orbit = r * np.cos(nu)
        y_orbit = r * np.sin(nu)
        text += "6. 计算轨道平面坐标：\n"
        text += "   x' = r·cos(ν)\n"
        text += "   y' = r·sin(ν)\n"
        text += f"   x' = {x_orbit:.6f} AU\n"
        text += f"   y' = {y_orbit:.6f} AU\n\n"
        
        # 7. 坐标变换到黄道平面
        current_pos = self.get_position(a, e, i, L, omega, Omega)
        text += "7. 坐标变换到黄道平面：\n"
        text += "   应用三次旋转变换：\n"
        text += "   - 绕z轴旋转(-ω)：近日点经度\n"
        text += "   - 绕x轴旋转(-i)：轨道倾角\n"
        text += "   - 绕z轴旋转(-Ω)：升交点黄经\n"
        text += f"   最终坐标：\n"
        text += f"   X = {current_pos[0]:.6f} AU\n"
        text += f"   Y = {current_pos[1]:.6f} AU\n"
        text += f"   Z = {current_pos[2]:.6f} AU\n\n"
        
        # 8. 轨道特征
        text += "8. 轨道特征：\n"
        text += f"  - 近日点距离 = {a * (1 - e):.6f} AU\n"
        text += f"  - 远日点距离 = {a * (1 + e):.6f} AU\n"
        text += f"  - 轨道周期 ≈ {a**1.5:.2f} 年\n\n"
        
        # 9. 轨道方程
        text += "9. 轨道方程：\n"
        text += "   极坐标形式：\n"
        text += "   r = a(1-e²)/(1 + e·cos(ν))\n\n"
        text += "   参数方程形式：\n"
        text += "   x = r·[cos(Ω)·cos(ω+ν) - sin(Ω)·sin(ω+ν)·cos(i)]\n"
        text += "   y = r·[sin(Ω)·cos(ω+ν) + cos(Ω)·sin(ω+ν)·cos(i)]\n"
        text += "   z = r·sin(ω+ν)·sin(i)\n\n"
        
        # 10. 具体轨道方程
        text += "10. 具体轨道方程（t为时间参数）：\n"
        text += "    平近点角随时间变化：\n"
        text += f"    M(t) = {M:.6f}° + 360°·t/T\n"
        text += f"    其中 T = {a**1.5:.2f} 年\n\n"
        
        text += "    将当前参数代入轨道方程：\n"
        text += "    求解开普勒方程得到偏近点角 E(t)：\n"
        text += f"    E(t) = M(t) + {e:.6f}·sin(E(t))\n\n"
        
        text += "    真近点角：\n"
        text += f"    ν(t) = 2·arctan(√({(1+e)/(1-e):.6f})·tan(E(t)/2))\n\n"
        
        text += "    日心距离：\n"
        text += f"    r(t) = {a:.6f}·(1 - {e:.6f}·cos(E(t))) AU\n\n"
        
        text += "    最终坐标方程：\n"
        cos_omega = np.cos(np.radians(omega))
        sin_omega = np.sin(np.radians(omega))
        cos_Omega = np.cos(np.radians(Omega))
        sin_Omega = np.sin(np.radians(Omega))
        cos_i = np.cos(np.radians(i))
        sin_i = np.sin(np.radians(i))
        
        text += "    X(t) = r(t)·["
        text += f"{cos_Omega*cos_omega - sin_Omega*sin_omega*cos_i:.6f}·cos(ν(t)) + "
        text += f"{-cos_Omega*sin_omega - sin_Omega*cos_omega*cos_i:.6f}·sin(ν(t))]\n\n"
        
        text += "    Y(t) = r(t)·["
        text += f"{sin_Omega*cos_omega + cos_Omega*sin_omega*cos_i:.6f}·cos(ν(t)) + "
        text += f"{-sin_Omega*sin_omega + cos_Omega*cos_omega*cos_i:.6f}·sin(ν(t))]\n\n"
        
        text += "    Z(t) = r(t)·["
        text += f"{sin_omega*sin_i:.6f}·cos(ν(t)) + "
        text += f"{cos_omega*sin_i:.6f}·sin(ν(t))]\n"
        
        self.calc_text.insert(1.0, text)
        self.calc_text.configure(state='disabled')

    def update_velocity_chart(self, a, e):
        """更新速度-距离关系图表"""
        # 清除之前的图表
        self.velocity_ax.clear()
        
        # 生成距离数据点（使用对数分布以更好地显示曲线形状）
        r_perihelion = a * (1 - e)
        r_aphelion = a * (1 + e)
        points = 1000  # 增加数据点数量
        
        # 使用对数分布的点以更好地显示近日点附近的变化
        distances = np.geomspace(r_perihelion, r_aphelion, points)
        
        # 计算速度
        GM_sun = 4 * np.pi * np.pi  # AU³/年²
        velocities = np.sqrt(GM_sun * (2/distances - 1/a))
        
        # 转换为km/s
        AU_to_km = 149597870.7
        year_to_sec = 365.25 * 24 * 3600
        velocities_kms = velocities * AU_to_km / year_to_sec
        
        # 绘制图表
        self.velocity_ax.plot(distances, velocities_kms, 'b-', linewidth=2)
        
        # 标记近日点和远日点
        v_perihelion = np.sqrt(GM_sun * (2/r_perihelion - 1/a)) * AU_to_km / year_to_sec
        v_aphelion = np.sqrt(GM_sun * (2/r_aphelion - 1/a)) * AU_to_km / year_to_sec
        
        self.velocity_ax.plot(r_perihelion, v_perihelion, 'ro', label='近日点')
        self.velocity_ax.plot(r_aphelion, v_aphelion, 'go', label='远日点')
        
        # 设置坐标轴标签和标题
        self.velocity_ax.set_xlabel('日距 (AU)')
        self.velocity_ax.set_ylabel('速度 (km/s)')
        self.velocity_ax.set_title('轨道速度随日距的变化')
        
        # 添加网格和图例
        self.velocity_ax.grid(True, linestyle='--', alpha=0.7)
        self.velocity_ax.legend()
        
        # 设置合适的坐标轴范围
        self.velocity_ax.set_xlim(r_perihelion * 0.9, r_aphelion * 1.1)
        min_velocity = np.min(velocities_kms)
        max_velocity = np.max(velocities_kms)
        velocity_range = max_velocity - min_velocity
        self.velocity_ax.set_ylim(min_velocity - velocity_range * 0.1, 
                                max_velocity + velocity_range * 0.1)
        
        # 刷新画布
        self.velocity_canvas.draw()

    def update_orbit(self):
        try:
            # 获取输入值
            a = float(self.entries["半长轴 (AU):"].get())
            e = float(self.entries["偏心率:"].get())
            i = float(self.entries["轨道倾角 (度):"].get())
            L = float(self.entries["平均经度 (度):"].get())
            omega = float(self.entries["近日点经度 (度):"].get())
            Omega = float(self.entries["升交点黄经 (度):"].get())
            
            # 绘制轨道
            self.plot_orbit(a, e, np.radians(i), np.radians(L), np.radians(omega), np.radians(Omega))
            
            # 更新计算过程说明
            self.update_calculation_text(a, e, i, L, omega, Omega)
            
            # 更新速度-距离关系图表
            self.update_velocity_chart(a, e)
            
        except ValueError as e:
            self.calc_text.configure(state='normal')
            self.calc_text.delete(1.0, tk.END)
            self.calc_text.insert(1.0, f"输入错误: {e}")
            self.calc_text.configure(state='disabled')

    def calculate_speed(self):
        """计算行星在指定时间的公转速度"""
        try:
            # 获取轨道参数
            a = float(self.entries["半长轴 (AU):"].get())
            e = float(self.entries["偏心率:"].get())
            i = float(self.entries["轨道倾角 (度):"].get()) * np.pi / 180
            L = float(self.entries["平均经度 (度):"].get()) * np.pi / 180
            omega = float(self.entries["近日点经度 (度):"].get()) * np.pi / 180
            Omega = float(self.entries["升交点黄经 (度):"].get()) * np.pi / 180
            
            # 获取用户输入的时间
            time_str = self.time_entry.get()
            t = Time(time_str)
            
            # 计算轨道周期（开普勒第三定律）
            T = a ** 1.5  # 轨道周期（年）
            
            # 获取当前位置（考虑时间因素）
            pos1 = self.get_position(a, e, i, L, omega, Omega, time=t)
            
            # 计算当前距离太阳的距离（AU）
            r = np.sqrt(np.sum(pos1**2))
            
            # 太阳引力常数（AU³/年²）
            GM_sun = 4 * np.pi * np.pi
            
            # 计算轨道速度（AU/年）
            # 使用轨道方程：v² = GM(2/r - 1/a)
            v = np.sqrt(GM_sun * (2/r - 1/a))
            
            # 转换为km/s
            AU_to_km = 149597870.7  # 1 AU = 149597870.7 km
            year_to_sec = 365.25 * 24 * 3600  # 1年 = 31557600秒
            v_kms = v * AU_to_km / year_to_sec
            
            # 计算近日点和远日点速度
            r_perihelion = a * (1 - e)  # 近日点距离
            r_aphelion = a * (1 + e)    # 远日点距离
            
            v_perihelion = np.sqrt(GM_sun * (2/r_perihelion - 1/a)) * AU_to_km / year_to_sec
            v_aphelion = np.sqrt(GM_sun * (2/r_aphelion - 1/a)) * AU_to_km / year_to_sec
            
            # 更新计算过程文本
            self.calc_text.configure(state='normal')
            self.calc_text.delete(1.0, tk.END)
            self.calc_text.insert(tk.END, f"速度计算过程：\n\n")
            self.calc_text.insert(tk.END, f"计算时间：{time_str}\n\n")
            self.calc_text.insert(tk.END, f"1. 轨道参数：\n")
            self.calc_text.insert(tk.END, f"   - 半长轴 = {a:.3f} AU\n")
            self.calc_text.insert(tk.END, f"   - 偏心率 = {e:.6f}\n")
            self.calc_text.insert(tk.END, f"   - 轨道周期 = {T:.2f} 年\n\n")
            self.calc_text.insert(tk.END, f"2. 距离：\n")
            self.calc_text.insert(tk.END, f"   - 当前日距 = {r:.3f} AU\n")
            self.calc_text.insert(tk.END, f"   - 近日点距离 = {r_perihelion:.3f} AU\n")
            self.calc_text.insert(tk.END, f"   - 远日点距离 = {r_aphelion:.3f} AU\n\n")
            self.calc_text.insert(tk.END, f"3. 轨道速度：\n")
            self.calc_text.insert(tk.END, f"   - 当前速度 = {v_kms:.2f} km/s\n")
            self.calc_text.insert(tk.END, f"   - 近日点速度 = {v_perihelion:.2f} km/s\n")
            self.calc_text.insert(tk.END, f"   - 远日点速度 = {v_aphelion:.2f} km/s\n")
            self.calc_text.configure(state='disabled')
            
            # 重新绘制轨道和行星位置
            self.plot_orbit(a, e, i, L, omega, Omega)
            
            # 在新位置绘制行星
            self.ax.scatter(pos1[0], pos1[1], pos1[2], color='blue', s=100)
            self.canvas.draw()
            
        except ValueError as e:
            self.calc_text.configure(state='normal')
            self.calc_text.delete(1.0, tk.END)
            self.calc_text.insert(tk.END, "错误：请确保所有输入都是有效的数值，且日期格式正确（YYYY-MM-DD）")
            self.calc_text.configure(state='disabled')

    def calculate_daily_values(self, planet_name=None):
        """计算指定行星公转一周期内每日的公转速度和距日距离"""
        if planet_name is None:
            # 如果没有指定行星，使用当前输入框中的参数
            try:
                a = float(self.entries["半长轴 (AU):"].get())
                e = float(self.entries["偏心率:"].get())
                i = float(self.entries["轨道倾角 (度):"].get())
                L = float(self.entries["平均经度 (度):"].get())
                omega = float(self.entries["近日点经度 (度):"].get())
                Omega = float(self.entries["升交点黄经 (度):"].get())
                planet_name = "当前行星"
            except ValueError:
                self.calc_text.configure(state='normal')
                self.calc_text.delete(1.0, tk.END)
                self.calc_text.insert(1.0, "错误：请先选择行星或输入有效的轨道参数")
                self.calc_text.configure(state='disabled')
                return
        else:
            # 使用指定行星的参数
            params = self.planet_params[planet_name]
            a, e, i, L, omega, Omega = params
        
        text = f"{planet_name}轨道每日数据（基于精确周期计算）：\n\n"
        
        # 计算轨道周期（开普勒第三定律）
        orbital_period = a ** 1.5  # 轨道周期（年）
        days_in_orbit = int(orbital_period * 365.25)  # 轨道周期（天）
        
        text += f"轨道周期：{orbital_period:.2f}年 ({days_in_orbit}天)\n"
        text += "说明：\n"
        text += "1. 速度单位：AU/年 = 天文单位/年，km/s = 千米/秒\n"
        text += "2. 距离单位：AU = 天文单位，km = 千米\n"
        text += f"3. 基于{planet_name}轨道精确周期（{orbital_period:.2f}年），轨道被分为{days_in_orbit}份\n"
        text += "4. 计算从近日点开始\n\n"
        
        text += "轨道日\t速度(AU/年)\t速度(km/s)\t日距(AU)\t日距(km)\t太阳日速度(km/s)\t角速度(°/天)\n"
        
        # 计算轨道分段数（每天一个数据点）
        segments = days_in_orbit
        
        # 计算每个时间点的数据
        for day in range(min(365, segments)):  # 限制输出到一年或轨道周期
            # 计算平近点角
            M = 2 * np.pi * day / segments
            
            # 求解开普勒方程得到偏近点角 E
            E = self.solve_kepler(M, e)
            
            # 计算真近点角
            nu = 2 * np.arctan(np.sqrt((1 + e)/(1 - e)) * np.tan(E/2))
            
            # 计算日心距离 (AU)
            r = a * (1 - e * np.cos(E))
            
            # 计算速度
            GM_sun = 4 * np.pi * np.pi  # AU³/年²
            v_au = np.sqrt(GM_sun * (2/r - 1/a))  # AU/年
            
            # 转换速度到 km/s
            AU_to_km = 149597870.7
            year_to_sec = 365.25 * 24 * 3600
            v_kms = v_au * AU_to_km / year_to_sec
            
            # 计算日心距离（km）
            r_km = r * AU_to_km
            
            # 计算太阳日速度 (考虑自转和公转的叠加)
            # 太阳自转周期约为27天
            solar_rotation_period = 27  # 天
            solar_rotation_speed = 2 * np.pi * r_km / (solar_rotation_period * 24 * 3600)  # km/s
            solar_day_speed = v_kms + solar_rotation_speed  # 太阳日速度
            
            # 添加到结果文本
            text += f"第{day + 1}日\t{v_au:.6f}\t{v_kms:.6f}\t{r:.6f}\t{r_km:.0f}\t{solar_day_speed:.6f}\n"
                
                # 如果数据太多，显示部分数据
                if day == 364 and segments > 365:
                    text += "...(数据省略)...\n"
                    break
            
            text += f"\n{planet_name}轨道特征：\n"
            text += f"近日点距离：{a * (1 - e):.6f} AU\n"
            text += f"远日点距离：{a * (1 + e):.6f} AU\n"
            text += f"轨道偏心率：{e:.6f}\n"
            text += f"轨道倾角：{i:.6f}度\n\n"
            text += "-" * 80 + "\n"
        
        # 更新文本显示
        self.calc_text.configure(state='normal')
        self.calc_text.delete(1.0, tk.END)
        self.calc_text.insert(1.0, text)
        self.calc_text.configure(state='disabled')

if __name__ == "__main__":
    root = tk.Tk()
    app = OrbitalCalculator(root)
    root.mainloop()
