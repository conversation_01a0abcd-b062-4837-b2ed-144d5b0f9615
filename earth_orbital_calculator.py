import numpy as np
import matplotlib.pyplot as plt
import tkinter as tk
from tkinter import ttk, messagebox
from matplotlib.backends.backend_tkagg import FigureCanvasTkAgg
from matplotlib.figure import Figure
import matplotlib as mpl
from scipy import stats

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei']  # 设置中文字体为黑体
plt.rcParams['axes.unicode_minus'] = False  # 解决负号显示问题

class EarthOrbitalCalculator:
    def __init__(self, root):
        self.root = root
        self.root.title("地球轨道计算器")
        
        # 设置默认轨道参数
        self.semi_major_axis = 1.0  # 天文单位 (AU)
        self.eccentricity = 0.9  # 地球轨道偏心率
        self.d_parameter = 0.5  # 新增参数d的默认值
        
        # 创建主窗口布局
        self.create_gui()
        
    def create_gui(self):
        # 创建左侧参数面板
        param_frame = ttk.LabelFrame(self.root, text="轨道参数")
        param_frame.pack(side=tk.LEFT, padx=10, pady=10, fill=tk.Y)
        
        # 半长轴输入
        ttk.Label(param_frame, text="半长轴 (AU):").pack(pady=5)
        self.a_var = tk.StringVar(value=str(self.semi_major_axis))
        self.a_entry = ttk.Entry(param_frame, textvariable=self.a_var)
        self.a_entry.pack(pady=5)
        
        # 偏心率输入
        ttk.Label(param_frame, text="偏心率:").pack(pady=5)
        self.e_var = tk.StringVar(value=str(self.eccentricity))
        self.e_entry = ttk.Entry(param_frame, textvariable=self.e_var)
        self.e_entry.pack(pady=5)
        
        # 新增参数d输入
        ttk.Label(param_frame, text="参数d:").pack(pady=5)
        self.d_var = tk.StringVar(value=str(self.d_parameter))
        self.d_entry = ttk.Entry(param_frame, textvariable=self.d_var)
        self.d_entry.pack(pady=5)
        
        # 计算按钮
        ttk.Button(param_frame, text="计算", command=self.calculate_and_plot).pack(pady=20)
        
        # 创建图表区域
        plot_frame = ttk.Frame(self.root)
        plot_frame.pack(side=tk.RIGHT, padx=10, pady=10, fill=tk.BOTH, expand=True)
        
        # 创建左右分隔的图表区域
        left_plot_frame = ttk.Frame(plot_frame)
        left_plot_frame.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        
        right_plot_frame = ttk.Frame(plot_frame)
        right_plot_frame.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        
        # 创建时间序列图
        self.fig1 = Figure(figsize=(8, 6))
        self.canvas1 = FigureCanvasTkAgg(self.fig1, master=left_plot_frame)
        self.canvas1.get_tk_widget().pack(fill=tk.BOTH, expand=True)
        
        # 创建v-r关系图
        self.fig2 = Figure(figsize=(6, 6))
        self.canvas2 = FigureCanvasTkAgg(self.fig2, master=right_plot_frame)
        self.canvas2.get_tk_widget().pack(fill=tk.BOTH, expand=True)
        
        # 创建坐标显示标签
        self.coord_label = ttk.Label(param_frame, text="坐标: ")
        self.coord_label.pack(pady=20)
        
        # 创建相关性分析标签
        self.correlation_label = ttk.Label(param_frame, text="相关性分析: ")
        self.correlation_label.pack(pady=20)
        
    def on_plot_click(self, event):
        if event.inaxes:
            self.coord_label.config(text=f'坐标: x={event.xdata:.4f}, y={event.ydata:.4f}')
            
    def calculate_orbital_parameters(self, days):
        """计算一年内的轨道参数"""
        # 开普勒方程求解
        M = 2 * np.pi * days / 365.25  # 平近点角（单位：弧度）
        E = M  # 初始猜测值
        
        # 迭代求解偏近点角（开普勒方程）
        for _ in range(10):
            E = M + self.eccentricity * np.sin(E)
        
        # 计算真近点角
        nu = 2 * np.arctan(np.sqrt((1 + self.eccentricity)/(1 - self.eccentricity)) * np.tan(E/2))
        
        # 计算日地距离 (AU)：开普勒轨道方程
        r = self.semi_major_axis * (1 - self.eccentricity**2) / (1 + self.eccentricity * np.cos(nu))
        
        # 计算轨道速度 (km/s)：基于能量守恒
        v = 29.78 * np.sqrt(2/r - 1/self.semi_major_axis)  # 29.78 km/s 是地球平均轨道速度
        
        # 计算新的参数：π(r-da)(r-da)
        diff = r - self.d_parameter * self.semi_major_axis
        new_param = np.pi * diff * diff
        
        # 计算飞行路径角
        flight_path_angle = np.arctan2(self.eccentricity * np.sin(nu), 1 + self.eccentricity * np.cos(nu))
        
        # 计算速度方向角度，并与参数相乘
        velocity_direction = nu + flight_path_angle
        new_param = 1/new_param 
        
        return nu, r, v, new_param
        
    def calculate_and_plot(self):
        try:
            # 获取输入参数
            self.semi_major_axis = float(self.a_var.get())
            self.eccentricity = float(self.e_var.get())
            self.d_parameter = float(self.d_var.get())
            
            # 验证参数范围
            if self.eccentricity < 0 or self.eccentricity >= 1:
                raise ValueError("偏心率必须在0到1之间")
            if self.semi_major_axis <= 0:
                raise ValueError("半长轴必须大于0")
            if self.d_parameter <= 0 or self.d_parameter >= 1:
                raise ValueError("参数d必须在0到1之间")
            
            # 计算一年的数据
            days = np.linspace(0, 365, 366)
            data = [self.calculate_orbital_parameters(d) for d in days]
            nu, r, v, new_param = zip(*data)
            
            # 计算v和new_param的乘积
            product = np.array(v) * np.array(new_param)
            
            # 计算变异系数（标准差/平均值）- 用于评估乘积的稳定性
            cv = np.std(product) / np.mean(product)
            
            # 计算相关性
            correlation_inverse = np.corrcoef(1/np.array(v), np.array(new_param))[0, 1]
            r_squared_inverse = correlation_inverse ** 2
            
            # 更新相关性分析标签
            self.correlation_label.config(text=(
                f'分析结果:\n'
                f'v与π(r-da)²的相关性分析:\n'
                f'相关系数 (与1/v): {correlation_inverse:.4f}\n'
                f'R² = {r_squared_inverse:.4f}\n'
                f'乘积变异系数: {cv:.6f}\n'
                f'(变异系数越接近0越好)\n'
                f'平均乘积值: {np.mean(product):.2f}'
            ))
            
            # 绘制时间序列图
            self.fig1.clear()
            ax1 = self.fig1.add_subplot(111)
            ax2 = ax1.twinx()
            ax3 = ax1.twinx()
            
            # 设置第三个y轴的位置
            ax3.spines['right'].set_position(('outward', 60))
            
            # 绘制日地距离变化（使用ax1）
            line1, = ax1.plot(days, r, 'b-', label='日地距离')
            ax1.scatter(days, r, c='blue', s=10)
            ax1.set_xlabel('天数')
            ax1.set_ylabel('日地距离 (AU)', color='blue')
            ax1.tick_params(axis='y', labelcolor='blue')
            
            # 绘制轨道速度变化（使用ax2）
            line2, = ax2.plot(days, v, 'r-', label='轨道速度')
            ax2.scatter(days, v, c='red', s=10)
            ax2.set_ylabel('轨道速度 (km/s)', color='red')
            ax2.tick_params(axis='y', labelcolor='red')
            
            # 绘制新参数变化（使用ax3）
            line3, = ax3.plot(days, new_param, 'g-', label=f'π(r-{self.d_parameter}a)²')
            ax3.scatter(days, new_param, c='green', s=10)
            ax3.set_ylabel(f'π(r-{self.d_parameter}a)²', color='green')
            ax3.tick_params(axis='y', labelcolor='green')
            
            # 添加总标题
            self.fig1.suptitle('轨道参数随时间变化', fontsize=12)
            
            # 合并图例
            lines = [line1, line2, line3]
            labels = [l.get_label() for l in lines]
            ax1.legend(lines, labels, loc='upper right')
            
            # 绘制v-r关系图
            self.fig2.clear()
            ax_scatter = self.fig2.add_subplot(111)
            ax_scatter.scatter(r, v, c='purple', s=20, alpha=0.5, label='数据点')
            ax_scatter.set_xlabel('日地距离 (AU)')
            ax_scatter.set_ylabel('轨道速度 (km/s)')
            ax_scatter.set_title('速度-距离关系图')
            ax_scatter.grid(True)
            
            # 添加趋势线
            z = np.polyfit(r, v, 1)
            p = np.poly1d(z)
            ax_scatter.plot(r, p(r), "r--", alpha=0.8, label=f'线性趋势线\ny = {z[0]:.2f}x + {z[1]:.2f}')
            
            # 添加1/r趋势线
            z_inverse = np.polyfit(1/np.array(r), v, 1)
            r_range = np.linspace(min(r), max(r), 100)
            ax_scatter.plot(r_range, z_inverse[0]/r_range + z_inverse[1], "g--", alpha=0.8, 
                          label=f'反比趋势线\ny = {z_inverse[0]:.2f}/x + {z_inverse[1]:.2f}')
            
            ax_scatter.legend()
            
            # 调整布局
            self.fig1.tight_layout()
            self.fig2.tight_layout()
            
            # 绑定点击事件
            self.canvas1.mpl_connect('button_press_event', self.on_plot_click)
            self.canvas2.mpl_connect('button_press_event', self.on_plot_click)
            
            # 刷新画布
            self.canvas1.draw()
            self.canvas2.draw()
            
        except ValueError as e:
            messagebox.showerror("错误", str(e))

if __name__ == "__main__":
    root = tk.Tk()
    app = EarthOrbitalCalculator(root)
    root.geometry("1600x800")  # 增加窗口宽度以适应两个图表
    root.mainloop()
