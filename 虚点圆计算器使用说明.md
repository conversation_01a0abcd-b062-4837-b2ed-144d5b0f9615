# 虚点圆射线交点计算器使用说明

## 概述

本程序是一个基于Tkinter的图形界面应用，用于计算行星轨道与虚点圆的射线交点。程序使用JPL官方标准轨道参数，支持多种行星的计算。

## 主要功能

### 1. 文件选择
- 支持选择CSV格式的星历表文件
- 自动识别文件格式并显示数据信息
- 支持JPL HORIZONS导出的CSV数据

### 2. 行星选择
- 内置6大行星的JPL官方标准轨道参数：
  - **地球**: a=1.00000261 AU, e=0.01671123
  - **火星**: a=1.52371034 AU, e=0.09339410
  - **金星**: a=0.72333566 AU, e=0.00677672
  - **水星**: a=0.38709927 AU, e=0.20563593
  - **木星**: a=5.20288700 AU, e=0.04838624
  - **土星**: a=9.53667594 AU, e=0.05386179

### 3. 轨道参数调整
- 可手动调整半长轴a和偏心率e
- 实时显示计算公式
- 支持自定义轨道参数

### 4. 虚点公式显示
程序显示以下公式：
```
虚点值公式：2ae
虚半径公式：2a - 2ae = 2a(1-e)
虚点坐标：(0, -2ae, 0)
虚点圆：在XY平面内，圆心为虚点，半径为虚半径
```

### 5. 计算结果展示
- **计算结果标签页**: 显示详细的计算参数和统计信息
- **可视化图表标签页**: 4个子图展示轨道、交点分布等
- **交点数据表标签页**: 完整的交点数据表格

## 使用步骤

### 步骤1：启动程序
```bash
python 虚点圆计算器_GUI.py
```

### 步骤2：选择星历表文件
1. 点击"选择文件"按钮
2. 选择CSV格式的星历表文件
3. 确认文件加载成功

### 步骤3：选择行星
1. 从下拉菜单选择行星
2. 系统自动填入JPL标准参数
3. 可手动调整参数值

### 步骤4：开始计算
1. 点击"开始计算"按钮
2. 等待计算完成
3. 查看结果

### 步骤5：查看结果
- **计算结果**: 轨道参数、虚点参数、交点统计
- **可视化图表**: 轨道图、交点分布图、统计图表
- **数据表格**: 详细的交点坐标数据

## 输出文件

程序会在`planet_data`目录下生成以下文件：

### 1. 交点数据CSV文件
- 文件名：`{行星名}_虚点圆交点数据.csv`
- 包含中文表头说明
- 详细的交点坐标数据

### 2. 计算参数文件
- 文件名：`{行星名}_虚点圆计算参数.txt`
- 包含所有计算参数
- 统计信息和结果摘要

## CSV文件表头说明

生成的CSV文件包含以下列：

| 列名 | 说明 |
|------|------|
| 天数 | 从第1天开始的序号 |
| 日期 | 对应的日期（TDB时间） |
| 儒略日 | 儒略日数 |
| 地球X坐标_AU | 地球X坐标，单位：天文单位 |
| 地球Y坐标_AU | 地球Y坐标，单位：天文单位 |
| 地球Z坐标_AU | 地球Z坐标，单位：天文单位 |
| 射线方向X | 射线X方向分量 |
| 射线方向Y | 射线Y方向分量 |
| 射线方向Z | 射线Z方向分量 |
| 交点数量 | 该射线与虚点圆的交点个数 |
| 交点1_X坐标_AU | 第1个交点X坐标 |
| 交点1_Y坐标_AU | 第1个交点Y坐标 |
| 交点1_Z坐标_AU | 第1个交点Z坐标 |

## 技术特点

### 1. 使用JPL官方标准值
- 采用JPL官方发布的行星轨道参数
- 确保计算结果的权威性和准确性

### 2. 射线交点算法
- 从太阳出发的射线与虚点圆相交
- 使用解析几何方法求解
- 数值稳定，适用于高精度计算

### 3. 图形界面友好
- 直观的操作界面
- 实时参数调整
- 多标签页结果展示

### 4. 数据格式标准
- CSV格式便于后续处理
- 中文表头说明清晰
- 完整的元数据记录

## 系统要求

### Python版本
- Python 3.6 或更高版本

### 依赖包
```bash
pip install pandas numpy matplotlib tkinter
```

### 操作系统
- Windows 10/11
- macOS 10.14+
- Linux (Ubuntu 18.04+)

## 故障排除

### 1. 文件加载失败
- 检查CSV文件格式
- 确认文件编码为UTF-8
- 验证列名是否正确

### 2. 计算错误
- 检查轨道参数是否合理
- 确认数据完整性
- 查看错误信息提示

### 3. 界面显示问题
- 检查系统字体支持
- 调整窗口大小
- 重启程序

## 联系支持

如有问题或建议，请联系开发团队。

---

**版本**: 1.0  
**更新日期**: 2024年  
**开发者**: Augment Agent
