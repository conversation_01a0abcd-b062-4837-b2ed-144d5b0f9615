#!/usr/bin/env python3
"""
处理从JPL HORIZONS手动下载的地球轨道数据
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import os
from datetime import datetime

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

def process_jpl_horizons_txt(txt_file_path, output_dir='planet_data'):
    """
    处理从JPL HORIZONS下载的TXT文件

    参数:
    txt_file_path: TXT文件路径
    output_dir: 输出目录

    返回:
    pandas.DataFrame: 处理后的数据
    """

    if not os.path.exists(txt_file_path):
        print(f"❌ 文件不存在: {txt_file_path}")
        return None

    print(f"正在处理JPL HORIZONS数据文件: {txt_file_path}")
    print("=" * 60)

    try:
        # 读取TXT文件
        with open(txt_file_path, 'r', encoding='utf-8') as f:
            lines = f.readlines()

        # 寻找数据开始和结束的行
        data_start_line = 0
        data_end_line = len(lines)

        for i, line in enumerate(lines):
            if '$$SOE' in line:  # Start of Ephemeris
                data_start_line = i + 1
            elif '$$EOE' in line:  # End of Ephemeris
                data_end_line = i
                break

        print(f"数据开始行: {data_start_line}")
        print(f"数据结束行: {data_end_line}")

        if data_start_line == 0:
            print("❌ 未找到数据标记 $$SOE")
            return None

        # 解析数据
        data_records = []

        # JPL HORIZONS TXT格式：每个时间点占3行
        # 第1行：JDTDB = 日期字符串
        # 第2行：X Y Z (位置，单位km)
        # 第3行：VX VY VZ (速度，单位km/s)
        # 第4行：LT RG RR (光时，距离，径向速度)

        i = data_start_line
        while i < data_end_line:
            try:
                # 第1行：时间信息
                time_line = lines[i].strip()
                if '=' in time_line:
                    parts = time_line.split('=')
                    jd_str = parts[0].strip()
                    date_str = parts[1].strip() if len(parts) > 1 else ""

                    # 提取儒略日期
                    jd = float(jd_str.split()[0])

                    # 第2行：位置 X Y Z (km)
                    i += 1
                    if i >= data_end_line:
                        break
                    pos_line = lines[i].strip()

                    # 解析格式：" X =-2.707874042320922E+07 Y =-1.503690857792647E+08 Z = 3.333179869441688E+04"
                    # 使用正则表达式或字符串分割
                    import re
                    x_match = re.search(r'X\s*=\s*([-+]?\d+\.?\d*[eE]?[-+]?\d*)', pos_line)
                    y_match = re.search(r'Y\s*=\s*([-+]?\d+\.?\d*[eE]?[-+]?\d*)', pos_line)
                    z_match = re.search(r'Z\s*=\s*([-+]?\d+\.?\d*[eE]?[-+]?\d*)', pos_line)

                    if x_match and y_match and z_match:
                        x_km = float(x_match.group(1))
                        y_km = float(y_match.group(1))
                        z_km = float(z_match.group(1))
                    else:
                        print(f"无法解析位置行: {pos_line}")
                        i += 1
                        continue

                    # 第3行：速度 VX VY VZ (km/s)
                    i += 1
                    if i >= data_end_line:
                        break
                    vel_line = lines[i].strip()

                    # 解析格式：" VX= 2.885327160373463E+01 VY=-5.289606605291794E+00 VZ= 1.763854476144466E-05"
                    vx_match = re.search(r'VX\s*=\s*([-+]?\d+\.?\d*[eE]?[-+]?\d*)', vel_line)
                    vy_match = re.search(r'VY\s*=\s*([-+]?\d+\.?\d*[eE]?[-+]?\d*)', vel_line)
                    vz_match = re.search(r'VZ\s*=\s*([-+]?\d+\.?\d*[eE]?[-+]?\d*)', vel_line)

                    if vx_match and vy_match and vz_match:
                        vx_kms = float(vx_match.group(1))
                        vy_kms = float(vy_match.group(1))
                        vz_kms = float(vz_match.group(1))
                    else:
                        print(f"无法解析速度行: {vel_line}")
                        i += 1
                        continue

                    # 第4行：LT RG RR
                    i += 1
                    if i >= data_end_line:
                        break
                    other_line = lines[i].strip()

                    # 解析格式：" LT= 5.096453455057683E+02 RG= 1.527878308374335E+08 RR= 9.217394973498733E-02"
                    lt_match = re.search(r'LT\s*=\s*([-+]?\d+\.?\d*[eE]?[-+]?\d*)', other_line)
                    rg_match = re.search(r'RG\s*=\s*([-+]?\d+\.?\d*[eE]?[-+]?\d*)', other_line)
                    rr_match = re.search(r'RR\s*=\s*([-+]?\d+\.?\d*[eE]?[-+]?\d*)', other_line)

                    if lt_match and rg_match and rr_match:
                        lt_s = float(lt_match.group(1))      # 光时(秒)
                        rg_km = float(rg_match.group(1))     # 距离(km)
                        rr_kms = float(rr_match.group(1))    # 径向速度(km/s)
                    else:
                        print(f"无法解析其他参数行: {other_line}")
                        i += 1
                        continue

                    # 转换单位：km -> AU, km/s -> AU/day
                    km_to_au = 1.0 / 149597870.7  # 1 AU = 149597870.7 km
                    kms_to_au_day = 86400.0 / 149597870.7  # 1 km/s = 86400 km/day / 149597870.7 km/AU

                    x_au = x_km * km_to_au
                    y_au = y_km * km_to_au
                    z_au = z_km * km_to_au
                    vx_au_day = vx_kms * kms_to_au_day
                    vy_au_day = vy_kms * kms_to_au_day
                    vz_au_day = vz_kms * kms_to_au_day
                    rg_au = rg_km * km_to_au

                    # 创建数据记录
                    record = {
                        'julian_date': jd,
                        'date': date_str,
                        'x_position_au': x_au,
                        'y_position_au': y_au,
                        'z_position_au': z_au,
                        'x_velocity_au_day': vx_au_day,
                        'y_velocity_au_day': vy_au_day,
                        'z_velocity_au_day': vz_au_day,
                        'distance_au': rg_au,
                        'range_rate_au_day': rr_kms * kms_to_au_day,
                        'light_time_sec': lt_s,
                        'x_position_km': x_km,
                        'y_position_km': y_km,
                        'z_position_km': z_km,
                        'x_velocity_km_s': vx_kms,
                        'y_velocity_km_s': vy_kms,
                        'z_velocity_km_s': vz_kms,
                        'distance_km': rg_km,
                        'range_rate_km_s': rr_kms
                    }

                    data_records.append(record)

                i += 1

            except (ValueError, IndexError) as e:
                print(f"解析第{i}行时出错: {e}")
                i += 1
                continue

        # 创建DataFrame
        df_processed = pd.DataFrame(data_records)

        print(f"✓ 成功解析数据，共 {len(df_processed)} 个时间点")
        print(f"数据列: {list(df_processed.columns)}")

        # 计算轨道速度 (km/s) - 直接使用km/s的速度分量
        if all(col in df_processed.columns for col in ['x_velocity_km_s', 'y_velocity_km_s', 'z_velocity_km_s']):
            df_processed['speed_km_s'] = np.sqrt(
                df_processed['x_velocity_km_s']**2 +
                df_processed['y_velocity_km_s']**2 +
                df_processed['z_velocity_km_s']**2
            )
            print("✓ 已计算轨道速度")

        # 距离已经从文件中读取，无需重新计算
        
        # 保存处理后的数据
        if not os.path.exists(output_dir):
            os.makedirs(output_dir)
        
        output_file = os.path.join(output_dir, '地球_2024_处理后数据.csv')
        df_processed.to_csv(output_file, index=False, encoding='utf-8-sig')
        print(f"✓ 处理后数据已保存到: {output_file}")
        
        # 显示数据摘要
        print_data_summary(df_processed)
        
        return df_processed
        
    except Exception as e:
        print(f"❌ 处理数据时发生错误: {e}")
        return None

def print_data_summary(df):
    """打印数据摘要"""
    print(f"\n地球轨道数据摘要:")
    print("=" * 40)
    print(f"数据点数: {len(df)}")
    print(f"时间跨度: {df.iloc[0]['date'] if 'date' in df.columns else '未知'} 到 {df.iloc[-1]['date'] if 'date' in df.columns else '未知'}")
    
    if 'distance_au' in df.columns:
        avg_dist = df['distance_au'].mean()
        min_dist = df['distance_au'].min()
        max_dist = df['distance_au'].max()
        eccentricity = (max_dist - min_dist) / (max_dist + min_dist)
        
        print(f"\n轨道参数:")
        print(f"平均日心距离: {avg_dist:.6f} AU ({avg_dist * 149597870.7:.0f} km)")
        print(f"近日点距离: {min_dist:.6f} AU ({min_dist * 149597870.7:.0f} km)")
        print(f"远日点距离: {max_dist:.6f} AU ({max_dist * 149597870.7:.0f} km)")
        print(f"轨道偏心率: {eccentricity:.6f}")
    
    if 'speed_km_s' in df.columns:
        avg_speed = df['speed_km_s'].mean()
        max_speed = df['speed_km_s'].max()
        min_speed = df['speed_km_s'].min()
        
        print(f"\n速度参数:")
        print(f"平均轨道速度: {avg_speed:.2f} km/s")
        print(f"最高轨道速度: {max_speed:.2f} km/s (近日点)")
        print(f"最低轨道速度: {min_speed:.2f} km/s (远日点)")

def create_orbit_visualization(df, output_dir='planet_data'):
    """创建轨道可视化图表"""
    if df is None:
        return
    
    print(f"\n正在创建轨道可视化图表...")
    
    # 检查必要的列是否存在
    required_cols = ['x_position_au', 'y_position_au']
    if not all(col in df.columns for col in required_cols):
        print("❌ 缺少位置数据，无法创建轨道图")
        return
    
    try:
        # 创建图表
        fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(15, 12))
        
        # 1. 轨道轨迹 (XY平面)
        ax1.plot(df['x_position_au'], df['y_position_au'], 'b-', linewidth=1, alpha=0.7)
        ax1.scatter(0, 0, color='yellow', s=200, label='太阳', zorder=5)
        ax1.scatter(df['x_position_au'].iloc[0], df['y_position_au'].iloc[0], 
                   color='green', s=50, label='起点', zorder=4)
        ax1.set_xlabel('X 位置 (AU)')
        ax1.set_ylabel('Y 位置 (AU)')
        ax1.set_title('地球轨道轨迹 (XY平面)')
        ax1.grid(True, alpha=0.3)
        ax1.axis('equal')
        ax1.legend()
        
        # 2. 日心距离随时间变化
        if 'distance_au' in df.columns:
            days = range(len(df))
            ax2.plot(days, df['distance_au'], 'r-', linewidth=1)
            ax2.set_xlabel('天数')
            ax2.set_ylabel('日心距离 (AU)')
            ax2.set_title('地球日心距离变化')
            ax2.grid(True, alpha=0.3)
        
        # 3. 轨道速度随时间变化
        if 'speed_km_s' in df.columns:
            days = range(len(df))
            ax3.plot(days, df['speed_km_s'], 'g-', linewidth=1)
            ax3.set_xlabel('天数')
            ax3.set_ylabel('轨道速度 (km/s)')
            ax3.set_title('地球轨道速度变化')
            ax3.grid(True, alpha=0.3)
        
        # 4. 速度-距离关系
        if 'distance_au' in df.columns and 'speed_km_s' in df.columns:
            ax4.scatter(df['distance_au'], df['speed_km_s'], alpha=0.6, s=10)
            ax4.set_xlabel('日心距离 (AU)')
            ax4.set_ylabel('轨道速度 (km/s)')
            ax4.set_title('速度-距离关系')
            ax4.grid(True, alpha=0.3)
        
        plt.tight_layout()
        
        # 保存图表
        chart_file = os.path.join(output_dir, '地球轨道分析图表.png')
        plt.savefig(chart_file, dpi=300, bbox_inches='tight')
        print(f"✓ 轨道分析图表已保存到: {chart_file}")
        
        plt.show()
        
    except Exception as e:
        print(f"❌ 创建图表时发生错误: {e}")

def main():
    """主函数"""
    print("JPL HORIZONS 地球轨道数据处理器")
    print("=" * 50)
    
    # 检查是否有下载的数据文件
    possible_files = [
        '星历表/地球_2024_JPL_HORIZONS.4hour.txt',  # 优先使用4小时间隔的数据（更详细）
        '星历表/地球公转20240101-20250101默认星历表.txt',
        'planet_data/地球_2024_JPL_HORIZONS.txt',
        '地球_2024_JPL_HORIZONS.txt',
        'horizons_results.txt'
    ]

    txt_file = None
    for file_path in possible_files:
        if os.path.exists(file_path):
            txt_file = file_path
            break

    if txt_file is None:
        print("❌ 未找到JPL HORIZONS数据文件")
        print("\n请确保已从JPL HORIZONS网站下载地球轨道数据，并保存为以下文件名之一:")
        for file_path in possible_files:
            print(f"  - {file_path}")
        print("\n下载指南请参考: JPL_HORIZONS_手动下载指南.md")
        return

    print(f"找到数据文件: {txt_file}")

    # 处理数据
    df = process_jpl_horizons_txt(txt_file)
    
    if df is not None:
        # 创建可视化图表
        create_orbit_visualization(df)
        
        print("\n🎉 地球轨道数据处理完成！")
        print("\n生成的文件:")
        print("  - planet_data/地球_2024_处理后数据.csv (处理后的数据)")
        print("  - planet_data/地球轨道分析图表.png (轨道分析图表)")
    else:
        print("\n❌ 数据处理失败")

if __name__ == "__main__":
    main()
