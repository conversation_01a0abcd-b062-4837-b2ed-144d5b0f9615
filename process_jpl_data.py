#!/usr/bin/env python3
"""
处理从JPL HORIZONS手动下载的地球轨道数据
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import os
from datetime import datetime

def process_jpl_horizons_csv(csv_file_path, output_dir='planet_data'):
    """
    处理从JPL HORIZONS下载的CSV文件
    
    参数:
    csv_file_path: CSV文件路径
    output_dir: 输出目录
    
    返回:
    pandas.DataFrame: 处理后的数据
    """
    
    if not os.path.exists(csv_file_path):
        print(f"❌ 文件不存在: {csv_file_path}")
        return None
    
    print(f"正在处理JPL HORIZONS数据文件: {csv_file_path}")
    print("=" * 60)
    
    try:
        # 读取CSV文件，通常需要跳过前面的注释行
        # JPL HORIZONS的CSV文件格式可能有所不同，需要根据实际情况调整
        
        # 先读取文件的前几行来确定数据开始的位置
        with open(csv_file_path, 'r', encoding='utf-8') as f:
            lines = f.readlines()
        
        # 寻找数据开始的行（通常包含$$SOE标记）
        data_start_line = 0
        data_end_line = len(lines)
        
        for i, line in enumerate(lines):
            if '$$SOE' in line:  # Start of Ephemeris
                data_start_line = i + 1
            elif '$$EOE' in line:  # End of Ephemeris
                data_end_line = i
                break
        
        print(f"数据开始行: {data_start_line}")
        print(f"数据结束行: {data_end_line}")
        
        # 读取数据部分
        if data_start_line > 0:
            # 如果找到了标记，读取标记之间的数据
            data_lines = lines[data_start_line:data_end_line]
            
            # 将数据写入临时文件
            temp_file = 'temp_data.csv'
            with open(temp_file, 'w', encoding='utf-8') as f:
                f.writelines(data_lines)
            
            # 读取临时文件
            df = pd.read_csv(temp_file)
            os.remove(temp_file)  # 删除临时文件
        else:
            # 如果没有找到标记，尝试直接读取
            df = pd.read_csv(csv_file_path)
        
        print(f"✓ 成功读取数据，共 {len(df)} 行")
        print(f"数据列: {list(df.columns)}")
        
        # 标准化列名（JPL HORIZONS的列名可能有所不同）
        column_mapping = {}
        
        # 常见的JPL HORIZONS列名映射
        for col in df.columns:
            col_lower = col.lower().strip()
            if 'jdtdb' in col_lower or 'jd' in col_lower:
                column_mapping[col] = 'julian_date'
            elif 'calendar' in col_lower or 'date' in col_lower:
                column_mapping[col] = 'date'
            elif col_lower == 'x':
                column_mapping[col] = 'x_position_au'
            elif col_lower == 'y':
                column_mapping[col] = 'y_position_au'
            elif col_lower == 'z':
                column_mapping[col] = 'z_position_au'
            elif col_lower == 'vx':
                column_mapping[col] = 'x_velocity_au_day'
            elif col_lower == 'vy':
                column_mapping[col] = 'y_velocity_au_day'
            elif col_lower == 'vz':
                column_mapping[col] = 'z_velocity_au_day'
            elif 'rg' in col_lower or 'range' in col_lower:
                column_mapping[col] = 'distance_au'
            elif 'rr' in col_lower:
                column_mapping[col] = 'range_rate_au_day'
        
        # 重命名列
        df_processed = df.rename(columns=column_mapping)
        
        # 计算轨道速度 (km/s)
        velocity_cols = ['x_velocity_au_day', 'y_velocity_au_day', 'z_velocity_au_day']
        if all(col in df_processed.columns for col in velocity_cols):
            # 1 AU/day = 1731.46 km/s
            au_day_to_km_s = 1731.46
            df_processed['speed_km_s'] = np.sqrt(
                df_processed['x_velocity_au_day']**2 + 
                df_processed['y_velocity_au_day']**2 + 
                df_processed['z_velocity_au_day']**2
            ) * au_day_to_km_s
            print("✓ 已计算轨道速度")
        
        # 计算距离（如果没有距离列）
        position_cols = ['x_position_au', 'y_position_au', 'z_position_au']
        if all(col in df_processed.columns for col in position_cols) and 'distance_au' not in df_processed.columns:
            df_processed['distance_au'] = np.sqrt(
                df_processed['x_position_au']**2 + 
                df_processed['y_position_au']**2 + 
                df_processed['z_position_au']**2
            )
            print("✓ 已计算日心距离")
        
        # 保存处理后的数据
        if not os.path.exists(output_dir):
            os.makedirs(output_dir)
        
        output_file = os.path.join(output_dir, '地球_2024_处理后数据.csv')
        df_processed.to_csv(output_file, index=False, encoding='utf-8-sig')
        print(f"✓ 处理后数据已保存到: {output_file}")
        
        # 显示数据摘要
        print_data_summary(df_processed)
        
        return df_processed
        
    except Exception as e:
        print(f"❌ 处理数据时发生错误: {e}")
        return None

def print_data_summary(df):
    """打印数据摘要"""
    print(f"\n地球轨道数据摘要:")
    print("=" * 40)
    print(f"数据点数: {len(df)}")
    print(f"时间跨度: {df.iloc[0]['date'] if 'date' in df.columns else '未知'} 到 {df.iloc[-1]['date'] if 'date' in df.columns else '未知'}")
    
    if 'distance_au' in df.columns:
        avg_dist = df['distance_au'].mean()
        min_dist = df['distance_au'].min()
        max_dist = df['distance_au'].max()
        eccentricity = (max_dist - min_dist) / (max_dist + min_dist)
        
        print(f"\n轨道参数:")
        print(f"平均日心距离: {avg_dist:.6f} AU ({avg_dist * 149597870.7:.0f} km)")
        print(f"近日点距离: {min_dist:.6f} AU ({min_dist * 149597870.7:.0f} km)")
        print(f"远日点距离: {max_dist:.6f} AU ({max_dist * 149597870.7:.0f} km)")
        print(f"轨道偏心率: {eccentricity:.6f}")
    
    if 'speed_km_s' in df.columns:
        avg_speed = df['speed_km_s'].mean()
        max_speed = df['speed_km_s'].max()
        min_speed = df['speed_km_s'].min()
        
        print(f"\n速度参数:")
        print(f"平均轨道速度: {avg_speed:.2f} km/s")
        print(f"最高轨道速度: {max_speed:.2f} km/s (近日点)")
        print(f"最低轨道速度: {min_speed:.2f} km/s (远日点)")

def create_orbit_visualization(df, output_dir='planet_data'):
    """创建轨道可视化图表"""
    if df is None:
        return
    
    print(f"\n正在创建轨道可视化图表...")
    
    # 检查必要的列是否存在
    required_cols = ['x_position_au', 'y_position_au']
    if not all(col in df.columns for col in required_cols):
        print("❌ 缺少位置数据，无法创建轨道图")
        return
    
    try:
        # 创建图表
        fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(15, 12))
        
        # 1. 轨道轨迹 (XY平面)
        ax1.plot(df['x_position_au'], df['y_position_au'], 'b-', linewidth=1, alpha=0.7)
        ax1.scatter(0, 0, color='yellow', s=200, label='太阳', zorder=5)
        ax1.scatter(df['x_position_au'].iloc[0], df['y_position_au'].iloc[0], 
                   color='green', s=50, label='起点', zorder=4)
        ax1.set_xlabel('X 位置 (AU)')
        ax1.set_ylabel('Y 位置 (AU)')
        ax1.set_title('地球轨道轨迹 (XY平面)')
        ax1.grid(True, alpha=0.3)
        ax1.axis('equal')
        ax1.legend()
        
        # 2. 日心距离随时间变化
        if 'distance_au' in df.columns:
            days = range(len(df))
            ax2.plot(days, df['distance_au'], 'r-', linewidth=1)
            ax2.set_xlabel('天数')
            ax2.set_ylabel('日心距离 (AU)')
            ax2.set_title('地球日心距离变化')
            ax2.grid(True, alpha=0.3)
        
        # 3. 轨道速度随时间变化
        if 'speed_km_s' in df.columns:
            days = range(len(df))
            ax3.plot(days, df['speed_km_s'], 'g-', linewidth=1)
            ax3.set_xlabel('天数')
            ax3.set_ylabel('轨道速度 (km/s)')
            ax3.set_title('地球轨道速度变化')
            ax3.grid(True, alpha=0.3)
        
        # 4. 速度-距离关系
        if 'distance_au' in df.columns and 'speed_km_s' in df.columns:
            ax4.scatter(df['distance_au'], df['speed_km_s'], alpha=0.6, s=10)
            ax4.set_xlabel('日心距离 (AU)')
            ax4.set_ylabel('轨道速度 (km/s)')
            ax4.set_title('速度-距离关系')
            ax4.grid(True, alpha=0.3)
        
        plt.tight_layout()
        
        # 保存图表
        chart_file = os.path.join(output_dir, '地球轨道分析图表.png')
        plt.savefig(chart_file, dpi=300, bbox_inches='tight')
        print(f"✓ 轨道分析图表已保存到: {chart_file}")
        
        plt.show()
        
    except Exception as e:
        print(f"❌ 创建图表时发生错误: {e}")

def main():
    """主函数"""
    print("JPL HORIZONS 地球轨道数据处理器")
    print("=" * 50)
    
    # 检查是否有下载的数据文件
    possible_files = [
        'planet_data/地球_2024_JPL_HORIZONS.csv',
        '地球_2024_JPL_HORIZONS.csv',
        'horizons_results.csv',
        '地球_2024_JPL_HORIZONS.4hour.txt'
    ]
    
    csv_file = None
    for file_path in possible_files:
        if os.path.exists(file_path):
            csv_file = file_path
            break
    
    if csv_file is None:
        print("❌ 未找到JPL HORIZONS数据文件")
        print("\n请确保已从JPL HORIZONS网站下载地球轨道数据，并保存为以下文件名之一:")
        for file_path in possible_files:
            print(f"  - {file_path}")
        print("\n下载指南请参考: JPL_HORIZONS_手动下载指南.md")
        return
    
    print(f"找到数据文件: {csv_file}")
    
    # 处理数据
    df = process_jpl_horizons_csv(csv_file)
    
    if df is not None:
        # 创建可视化图表
        create_orbit_visualization(df)
        
        print("\n🎉 地球轨道数据处理完成！")
        print("\n生成的文件:")
        print("  - planet_data/地球_2024_处理后数据.csv (处理后的数据)")
        print("  - planet_data/地球轨道分析图表.png (轨道分析图表)")
    else:
        print("\n❌ 数据处理失败")

if __name__ == "__main__":
    main()
