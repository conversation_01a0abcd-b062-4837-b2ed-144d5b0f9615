#!/usr/bin/env python3
"""
测试虚点圆计算器GUI程序
"""

import sys
import os

# 添加当前目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

try:
    # 导入GUI程序
    from 虚点圆计算器_GUI import VirtualPointCalculator
    import tkinter as tk
    
    print("✓ 成功导入GUI模块")
    
    # 创建测试窗口
    root = tk.Tk()
    app = VirtualPointCalculator(root)
    
    print("✓ 成功创建GUI应用")
    print("✓ GUI程序已启动，请在窗口中测试功能")
    print("\n测试步骤：")
    print("1. 点击'选择文件'按钮，选择CSV文件")
    print("2. 选择行星（默认为地球）")
    print("3. 可以调整轨道参数（使用JPL标准值）")
    print("4. 点击'开始计算'按钮")
    print("5. 查看计算结果、可视化图表和数据表")
    
    # 启动GUI
    root.mainloop()
    
except ImportError as e:
    print(f"❌ 导入错误: {e}")
    print("请确保所有依赖包已安装：")
    print("pip install pandas numpy matplotlib tkinter")
    
except Exception as e:
    print(f"❌ 程序错误: {e}")
    import traceback
    traceback.print_exc()
