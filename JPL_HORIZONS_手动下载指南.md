# JPL HORIZONS 地球轨道数据手动下载指南

由于网络连接问题，我们需要通过浏览器手动从JPL HORIZONS下载地球轨道数据。

## 网站地址
https://ssd.jpl.nasa.gov/horizons/app.html#/

## 下载步骤

### 1. 选择目标天体
- 在 "Target Body" 字段中输入: `Earth` 或 `399`
- 系统会自动识别为地球

### 2. 设置观测位置
- 在 "Observer Location" 中选择: `Solar System Barycenter (@0)` 或 `Sun (@sun)`
- 这将给出日心坐标系的数据

### 3. 设置时间范围
- Start Time: `2024-01-01`
- Stop Time: `2024-12-31`
- Step Size: `1 d` (每天一个数据点)

### 4. 选择数据类型
- 选择 "Vector Table" (矢量表)
- 这将提供位置和速度数据

### 5. 设置输出格式
- 在 "Table Settings" 中:
  - Reference Frame: `ICRF/J2000.0`
  - Reference Plane: `Ecliptic`
  - Output Units: `AU and AU/day`
  - CSV Format: 选中 (便于后续处理)

### 6. 生成数据
- 点击 "Generate Ephemeris" 按钮
- 等待数据生成完成

### 7. 下载数据
- 数据生成后，点击 "Download Results" 
- 选择 CSV 格式下载
- 保存文件名建议: `地球_2024_JPL_HORIZONS.csv`

## 数据说明

下载的CSV文件将包含以下列：

### 时间信息
- `JDTDB`: 儒略日期 (Barycentric Dynamical Time)
- `Calendar Date (TDB)`: 日历日期

### 位置坐标 (AU)
- `X`: X轴位置 (天文单位)
- `Y`: Y轴位置 (天文单位) 
- `Z`: Z轴位置 (天文单位)

### 速度矢量 (AU/day)
- `VX`: X轴速度 (天文单位/天)
- `VY`: Y轴速度 (天文单位/天)
- `VZ`: Z轴速度 (天文单位/天)

### 其他参数
- `LT`: 光时 (分钟)
- `RG`: 距离 (天文单位)
- `RR`: 径向速度 (天文单位/天)

## 数据处理

下载完成后，可以使用以下Python脚本处理数据：

```python
import pandas as pd
import numpy as np

# 读取下载的CSV文件
df = pd.read_csv('地球_2024_JPL_HORIZONS.csv', skiprows=1)  # 跳过标题行

# 计算轨道速度 (km/s)
au_day_to_km_s = 1731.46  # 转换因子
df['speed_km_s'] = np.sqrt(df['VX']**2 + df['VY']**2 + df['VZ']**2) * au_day_to_km_s

# 计算距离 (如果RG列不存在)
if 'RG' not in df.columns:
    df['distance_au'] = np.sqrt(df['X']**2 + df['Y']**2 + df['Z']**2)

# 显示基本统计
print("地球轨道参数统计:")
print(f"平均日心距离: {df['RG'].mean():.6f} AU")
print(f"近日点距离: {df['RG'].min():.6f} AU") 
print(f"远日点距离: {df['RG'].max():.6f} AU")
print(f"平均轨道速度: {df['speed_km_s'].mean():.2f} km/s")
```

## 注意事项

1. **数据精度**: JPL HORIZONS提供的是高精度的天体力学数据
2. **坐标系**: 使用的是国际天球参考框架 (ICRF)
3. **时间系统**: 使用质心动力学时间 (TDB)
4. **单位**: 位置单位为天文单位(AU)，速度单位为AU/day

## 文件保存位置

请将下载的文件保存到项目的 `planet_data/` 目录中，文件名建议：
- `地球_2024_JPL_HORIZONS.csv`

这样可以与现有的数据处理脚本保持一致。
