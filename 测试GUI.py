#!/usr/bin/env python3
"""
测试太阳圈虚点圈计算器GUI
"""

import tkinter as tk
from tkinter import ttk, filedialog, messagebox, scrolledtext
import pandas as pd
import os
import subprocess
import sys

class TestCalculatorGUI:
    def __init__(self, root):
        self.root = root
        self.root.title("太阳圈虚点圈计算器测试")
        self.root.geometry("800x600")
        
        self.df = None
        self.setup_ui()
        
    def setup_ui(self):
        """设置用户界面"""
        
        # 创建主框架
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # 1. 文件选择区域
        file_frame = ttk.LabelFrame(main_frame, text="1. 选择星历表CSV文件", padding="5")
        file_frame.pack(fill=tk.X, pady=5)
        
        ttk.Button(file_frame, text="选择文件", command=self.select_file).pack(side=tk.LEFT, padx=5)
        self.file_label = ttk.Label(file_frame, text="未选择文件")
        self.file_label.pack(side=tk.LEFT, padx=5)
        
        # 2. 计算按钮
        ttk.Button(main_frame, text="开始计算", command=self.calculate).pack(pady=10)
        
        # 3. 结果显示区域
        result_frame = ttk.LabelFrame(main_frame, text="计算结果", padding="5")
        result_frame.pack(fill=tk.BOTH, expand=True, pady=5)
        
        self.result_text = scrolledtext.ScrolledText(result_frame, height=20, width=80)
        self.result_text.pack(fill=tk.BOTH, expand=True)
        
    def select_file(self):
        """选择CSV文件"""
        file_path = filedialog.askopenfilename(
            title="选择星历表CSV文件",
            filetypes=[("CSV files", "*.csv"), ("All files", "*.*")]
        )
        
        if file_path:
            try:
                self.df = pd.read_csv(file_path)
                self.file_label.config(text=f"已选择: {os.path.basename(file_path)}")
                self.result_text.insert(tk.END, f"✓ 成功加载文件: {os.path.basename(file_path)}\n")
                self.result_text.insert(tk.END, f"  数据点数量: {len(self.df)}\n")
                self.result_text.insert(tk.END, f"  列名: {list(self.df.columns)}\n\n")
                self.result_text.see(tk.END)
            except Exception as e:
                messagebox.showerror("错误", f"加载文件失败: {e}")
                
    def calculate(self):
        """执行计算 - 调用外部计算脚本"""
        
        if self.df is None:
            messagebox.showerror("错误", "请先选择星历表文件")
            return
            
        # 清空结果显示
        self.result_text.delete(1.0, tk.END)
        
        # 调用外部计算脚本
        self.result_text.insert(tk.END, "正在调用太阳圈虚点圈交点计算程序...\n")
        self.result_text.see(tk.END)
        self.root.update()
        
        try:
            # 运行外部计算脚本
            result = subprocess.run([
                sys.executable, "太阳圈虚点圈交点计算.py"
            ], capture_output=True, text=True, cwd=os.getcwd())
            
            if result.returncode == 0:
                self.result_text.insert(tk.END, "✓ 计算完成！\n\n")
                self.result_text.insert(tk.END, "=== 计算程序输出 ===\n")
                self.result_text.insert(tk.END, result.stdout)
                
                # 检查生成的文件
                self.check_output_files()
                
            else:
                self.result_text.insert(tk.END, f"❌ 计算失败:\n{result.stderr}\n")
                
        except Exception as e:
            self.result_text.insert(tk.END, f"❌ 调用计算程序失败: {e}\n")
            
        self.result_text.see(tk.END)
    
    def check_output_files(self):
        """检查输出文件"""
        
        self.result_text.insert(tk.END, "\n=== 检查输出文件 ===\n")
        
        output_files = [
            "planet_data/太阳圈虚点圈交点数据.csv",
            "planet_data/太阳圈虚点圈计算参数.txt",
            "planet_data/太阳圈虚点圈交点分析图表.png",
            "planet_data/太阳圈虚点圈交点计算分析报告.md"
        ]
        
        for file_path in output_files:
            if os.path.exists(file_path):
                file_size = os.path.getsize(file_path)
                self.result_text.insert(tk.END, f"✓ {file_path} ({file_size} bytes)\n")
            else:
                self.result_text.insert(tk.END, f"❌ {file_path} (未找到)\n")
        
        # 如果CSV文件存在，显示一些统计信息
        csv_file = "planet_data/太阳圈虚点圈交点数据.csv"
        if os.path.exists(csv_file):
            try:
                df = pd.read_csv(csv_file, comment='#')
                self.result_text.insert(tk.END, f"\n=== CSV文件统计 ===\n")
                self.result_text.insert(tk.END, f"数据行数: {len(df)}\n")
                self.result_text.insert(tk.END, f"列数: {len(df.columns)}\n")
                self.result_text.insert(tk.END, f"列名: {list(df.columns)[:5]}...\n")  # 只显示前5个列名
                
                # 显示一些关键统计
                if '太阳圈交点数量' in df.columns:
                    sun_total = df['太阳圈交点数量'].sum()
                    self.result_text.insert(tk.END, f"太阳圈总交点数: {sun_total}\n")
                
                if '虚点圈交点数量' in df.columns:
                    virtual_total = df['虚点圈交点数量'].sum()
                    self.result_text.insert(tk.END, f"虚点圈总交点数: {virtual_total}\n")
                
                if '虚点额外点数量' in df.columns:
                    extra_total = df['虚点额外点数量'].sum()
                    self.result_text.insert(tk.END, f"虚点额外点总数: {extra_total}\n")
                
                if '虚点圈弧距_AU' in df.columns:
                    arc_distances = df['虚点圈弧距_AU'].dropna()
                    if len(arc_distances) > 0:
                        avg_arc = arc_distances.mean()
                        self.result_text.insert(tk.END, f"平均弧距: {avg_arc:.6f} AU\n")
                
            except Exception as e:
                self.result_text.insert(tk.END, f"❌ 读取CSV文件失败: {e}\n")

def main():
    """主函数"""
    root = tk.Tk()
    app = TestCalculatorGUI(root)
    root.mainloop()

if __name__ == "__main__":
    main()
