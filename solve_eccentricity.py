import numpy as np
from scipy.optimize import fsolve

def solve_eccentricity():
    def equations(vars):
        E, e = vars
        
        # 方程1: 2e = (cos E - e)
        eq1 = 2*e - (np.cos(E) - e)
        
        # 方程2: 1 - e² = sin E
        eq2 = (1 - e**2) - np.sin(E)
        
        return [eq1, eq2]

    # 初始猜测值
    initial_guess = [1.0, 0.5]  # 初始猜测值 [E, e]
    
    # 求解方程组
    solution = fsolve(equations, initial_guess)
    E, e = solution
    
    return E, e

if __name__ == "__main__":
    try:
        E, e = solve_eccentricity()
        print(f"\n求解结果:")
        print(f"偏心角 E = {E:.6f} rad = {np.degrees(E):.6f}°")
        print(f"偏心率 e = {e:.6f}")
        
        # 验证结果
        eq1_error = abs(2*e - (np.cos(E) - e))
        eq2_error = abs((1 - e**2) - np.sin(E))
        print(f"\n方程误差:")
        print(f"方程1误差: {eq1_error:.2e}")
        print(f"方程2误差: {eq2_error:.2e}")
    except Exception as err:
        print(f"计算出错: {err}")
