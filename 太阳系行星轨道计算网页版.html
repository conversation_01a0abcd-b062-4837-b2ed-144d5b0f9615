<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>太阳系轨道计算器</title>
    <style>
        body { margin: 0; }
        #container {
            width: 100%;
            height: 100vh;
            display: flex;
        }
        #controls {
            width: 700px;
            padding: 20px;
            box-sizing: border-box;
            background: #f5f5f5;
            overflow-y: auto;
            display: flex;
            flex-direction: column;
            position: relative;  
        }
        .param-group {
            display: flex;
            margin-bottom: 10px;
            justify-content: space-between;
        }
        .param-item {
            flex: 1;
            margin-right: 10px;
        }
        .param-item:last-child {
            margin-right: 0;
        }
        .param-item label {
            display: block;
            margin-bottom: 5px;
            font-family: "Microsoft YaHei", sans-serif;
            font-size: 14px;
        }
        .param-item input {
            width: 90%;
            padding: 5px;
            font-family: "Microsoft YaHei", sans-serif;
            font-size: 14px;
        }
        #canvas {
            flex-grow: 1;
            display: flex;
            flex-direction: column;
        }
        #orbitView {
            flex: 2;
        }
        #speedChart {
            flex: 1;
            min-height: 300px;
            margin: 10px;
            padding: 10px;
            background: white;
            border-radius: 5px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        #info {
            width: 400px;
            padding: 20px;
            background: #f5f5f5;
            overflow-y: auto;
            font-family: "Microsoft YaHei", sans-serif;
            font-size: 14px;
        }
        .planet-button {
            text-align: left;
            background: #e0e0e0;
            border: none;
            border-radius: 4px;
            margin: 5px;
            padding: 8px 15px;
            cursor: pointer;
        }
        .planet-button:hover {
            background: #d0d0d0;
        }
        .planet-button.active {
            background: #c0c0c0;
        }
        .drag-hint {
            text-align: center;
            color: #666;
            padding: 10px;
            font-size: 12px;
            border-top: 1px solid #ddd;
            margin-top: auto;  
            font-family: "Microsoft YaHei", sans-serif;
        }
        .drag-hint i {
            display: block;
            font-style: normal;
            font-size: 16px;
            margin-bottom: 5px;
            color: #999;
        }
    </style>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/three.js/r128/three.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/dat-gui/0.7.7/dat.gui.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/three@0.128.0/examples/js/controls/OrbitControls.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
</head>
<body>
    <div id="container">
        <div id="controls">
            <h2 style="font-family: 'Microsoft YaHei';">轨道参数</h2>
            <div class="param-group">
                <div class="param-item">
                    <label>半长轴 (AU)</label>
                    <input type="number" id="semiMajorAxis" step="0.00000001" value="1.00000011">
                </div>
                <div class="param-item">
                    <label>偏心率</label>
                    <input type="number" id="eccentricity" step="0.00000001" value="0.01671022">
                </div>
            </div>
            <div class="param-group">
                <div class="param-item">
                    <label>轨道倾角 (度)</label>
                    <input type="number" id="inclination" step="0.00000001" value="0.00005">
                </div>
                <div class="param-item">
                    <label>平均经度 (度)</label>
                    <input type="number" id="meanLongitude" step="0.00000001" value="100.46435">
                </div>
            </div>
            <div class="param-group">
                <div class="param-item">
                    <label>近日点经度 (度)</label>
                    <input type="number" id="perihelionLongitude" step="0.00000001" value="102.94719">
                </div>
                <div class="param-item">
                    <label>升交点黄经 (度)</label>
                    <input type="number" id="ascendingNode" step="0.00000001" value="-11.26064">
                </div>
            </div>
            <div class="param-group">
                <div class="param-item">
                    <label>计算时间</label>
                    <input type="date" id="calculationTime" value="">
                </div>
            </div>
            <div class="param-group">
                <button onclick="updateOrbit()" style="margin-right: 10px;">更新轨道</button>
                <button onclick="calculateSpeed()">计算速度</button>
            </div>
            
            <h2 style="font-family: 'Microsoft YaHei';">太阳系天体</h2>
            <div id="planetButtons"></div>
            
            <!-- 添加拖动提示 -->
            <div class="drag-hint">
                <i>⋮⋮</i>
                中间窗口太阳和轨道 可上下拖动查看更多
            </div>
        </div>
        <div id="canvas">
            <div id="orbitView"></div>
            <div id="speedChart">
                <canvas id="velocityChart"></canvas>
            </div>
        </div>
        <div id="info">
            <h2 style="font-family: 'Microsoft YaHei';">计算过程</h2>
            <div id="calculation"></div>
        </div>
    </div>

    <script>
        // 行星数据
        const planets = {
            "水星": [0.38709927,0.205636,7.004979,252.2503235,77.45779628,48.33076593],
            "金星": [0.72333566,0.006777,3.394676,181.9790995,131.6024672,76.67984255],
            "地球": [1.00000261,0.016711,-0.000015,100.4645717,102.9376819,0],
            "火星": [1.52371034,0.093394,1.849691,-4.55343205,-23.94362959,49.55953891],
            "木星": [5.202887,0.048386,1.304397,34.39644051,14.72847983,100.4739091],
            "土星": [9.53667594,0.053862,2.485992,49.95424423,92.59887831,113.6624245],
            "天王星": [19.18916464,0.047257,0.772638,313.2381045,170.9542763,74.01692503],
            "海王星": [30.06992276,0.00859,1.770043,-55.1200297,44.96476227,131.7842257],
            "冥王星": [39.54,0.24905,17.1405,110.30347,113.834,110.299]
        };

        // 行星颜色配置
        const planetColors = {
            "水星": 0x808080,  // 灰色（类似水星表面）
            "金星": 0xffd700,  // 金色（反映其明亮的大气）
            "地球": 0x4169e1,  // 皇家蓝（代表海洋）
            "火星": 0xff4500,  // 红橙色（火星的红色表面）
            "木星": 0xdeb887,  // 实木色（木星的条纹）
            "土星": 0xf4a460,  // 沙褐色（土星的主要颜色）
            "天王星": 0x40e0d0,  // 绿松石色（天王星的蓝绿色）
            "海王星": 0x0000cd,  // 深蓝色（深海蓝）
            "冥王星": 0x8b4513   // 马鞍棕色（冥王星的褐色）
        };

        // Three.js 变量
        let scene, camera, renderer, controls;
        let orbit, sun, aphelion, planet;
        let eclipticPlane, seasonLabels = [];

        // 初始化Three.js场景
        function init() {
            // 创建场景
            scene = new THREE.Scene();
            scene.background = new THREE.Color(0xffffff);

            // 创建相机（从春分点偏45度俯视）
            camera = new THREE.PerspectiveCamera(75, (window.innerWidth - 700) / window.innerHeight, 0.1, 1000);
            const verticalAngle = 45 * Math.PI / 180;  // 45度俯角
            const horizontalAngle = 0;  // 春分点方向（0度）
            const distance = 10;
            camera.position.set(
                distance * Math.sin(verticalAngle) * Math.cos(horizontalAngle), // x
                distance * Math.sin(verticalAngle) * Math.sin(horizontalAngle), // y
                distance * Math.cos(verticalAngle)  // z
            );
            camera.up.set(0, 0, 1);
            camera.lookAt(0, 0, 0);

            // 创建渲染器
            renderer = new THREE.WebGLRenderer({ antialias: true });
            renderer.setSize(window.innerWidth - 700, window.innerHeight * 0.7);  // 调整高度为70%
            document.getElementById('orbitView').appendChild(renderer.domElement);

            // 添加轨道控制，移除所有限制
            controls = new THREE.OrbitControls(camera, renderer.domElement);
            controls.enableDamping = true;
            controls.dampingFactor = 0.05;
            controls.screenSpacePanning = true;
            
            // 移除所有限制
            controls.minPolarAngle = 0;
            controls.maxPolarAngle = Math.PI;
            controls.minAzimuthAngle = -Infinity;
            controls.maxAzimuthAngle = Infinity;

            // 创建行星按钮
            createPlanetButtons();

            // 初始化轨道
            updateOrbit();

            // 添加窗口大小调整监听
            window.addEventListener('resize', onWindowResize, false);

            // 开始动画循环
            animate();

            // 初始化速度图表
            initVelocityChart();
        }

        // 动画循环
        function animate() {
            requestAnimationFrame(animate);
            if (controls) {  
                controls.update();
            }
            renderer.render(scene, camera);
        }

        // 窗口大小调整
        function onWindowResize() {
            if (camera && renderer) {  
                camera.aspect = (window.innerWidth - 700) / window.innerHeight;
                camera.updateProjectionMatrix();
                renderer.setSize(window.innerWidth - 700, window.innerHeight * 0.7);  // 调整高度为70%
            }
        }

        // 创建文本纹理
        function createTextTexture(text) {
            const canvas = document.createElement('canvas');
            const context = canvas.getContext('2d');
            canvas.width = 512;
            canvas.height = 256;
            
            context.font = 'bold 80px Microsoft YaHei';  // 固定16号字体
            context.fillStyle = '#000000';
            context.textAlign = 'center';
            context.textBaseline = 'middle';
            
            // 清除背景（设为透明）
            context.clearRect(0, 0, canvas.width, canvas.height);
            
            // 绘制文字
            context.fillText(text, 256, 128);
            
            const texture = new THREE.CanvasTexture(canvas);
            texture.needsUpdate = true;
            return texture;
        }

        // 创建行星按钮
        function createPlanetButtons() {
            const container = document.getElementById('planetButtons');
            for (let planet in planets) {
                const button = document.createElement('button');
                button.className = 'planet-button';
                button.textContent = planet;
                button.onclick = () => setPlanetParams(planet);
                if (planet === "地球") {
                    button.classList.add('active');
                }
                container.appendChild(button);
            }
        }

        // 设置行星参数
        function setPlanetParams(planet) {
            // 移除所有按钮的active类
            const buttons = document.getElementsByTagName('button');
            for (let button of buttons) {
                button.classList.remove('active');
                if (button.textContent === planet) {
                    button.classList.add('active');
                }
            }

            // 设置参数
            const params = planets[planet];
            document.getElementById('semiMajorAxis').value = params[0];
            document.getElementById('eccentricity').value = params[1];
            document.getElementById('inclination').value = params[2];
            document.getElementById('meanLongitude').value = params[3];
            document.getElementById('perihelionLongitude').value = params[4];
            document.getElementById('ascendingNode').value = params[5];

            // 更新轨道
            updateOrbit();
        }

        // 添加季节标记
        function addSeasonMarkers(r_ecliptic) {
            const seasons = {
                "春分 (0°)": new THREE.Vector3(r_ecliptic, 0, 0),
                "夏至 (90°)": new THREE.Vector3(0, r_ecliptic, 0),
                "秋分 (180°)": new THREE.Vector3(-r_ecliptic, 0, 0),
                "冬至 (270°)": new THREE.Vector3(0, -r_ecliptic, 0)
            };

            const spriteScale = r_ecliptic * 0.3;  // 调整标签大小
            for (const [text, position] of Object.entries(seasons)) {
                const sprite = new THREE.Sprite(
                    new THREE.SpriteMaterial({
                        map: createTextTexture(text),
                        transparent: true  // 启用透明
                    })
                );
                sprite.position.copy(position);
                sprite.scale.set(spriteScale, spriteScale * 0.5, 1);
                scene.add(sprite);
                seasonLabels.push(sprite);
            }
        }

        // 设置相机以显示完整轨道
        function fitCameraToOrbit(a) {
            if (camera && controls) {
                const distance = Math.max(a * 2.5, 5);
                camera.position.set(0, 0, distance);  
                controls.target.set(0, 0, 0);
                camera.lookAt(0, 0, 0);
                camera.updateProjectionMatrix();
                controls.update();
            }
        }

        // 计算屏幕固定大小
        function getScreenConstantSize() {
            const vFOV = camera.fov * Math.PI / 180;  
            const height = 2 * Math.tan(vFOV / 2);  
            return height * 0.05;  
        }

        // 更新轨道
        function updateOrbit() {
            // 获取参数
            const a = parseFloat(document.getElementById('semiMajorAxis').value);
            const e = parseFloat(document.getElementById('eccentricity').value);
            const i = parseFloat(document.getElementById('inclination').value) * Math.PI / 180;
            const L = parseFloat(document.getElementById('meanLongitude').value) * Math.PI / 180;
            const omega = parseFloat(document.getElementById('perihelionLongitude').value) * Math.PI / 180;
            const Omega = parseFloat(document.getElementById('ascendingNode').value) * Math.PI / 180;

            // 获取当前选中的行星（从按钮组中获取）
            let selectedPlanet = "地球";  // 默认为地球
            const buttons = document.getElementsByTagName('button');
            for (let button of buttons) {
                if (button.textContent in planets) {  // 检查是否是行星按钮
                    if (button.classList.contains('active')) {  // 检查是否是激活状态
                        selectedPlanet = button.textContent;
                        break;
                    }
                }
            }

            // 清除现有轨道
            if (orbit) scene.remove(orbit);
            if (aphelion) scene.remove(aphelion);
            seasonLabels.forEach(label => scene.remove(label));
            if (eclipticPlane) scene.remove(eclipticPlane);
            if (sun) scene.remove(sun);
            if (planet) scene.remove(planet);
            seasonLabels = [];

            // 计算物体大小（与半长轴成比例）
            const baseSize = a * 0.1;
            const sunSize = Math.max(baseSize * 0.5, 0.1);
            const planetSize = Math.max(baseSize * 0.3, 0.05);

            // 创建太阳（金黄色）
            const sunGeometry = new THREE.SphereGeometry(sunSize, 32, 32);
            const sunMaterial = new THREE.MeshBasicMaterial({ color: 0xffd700 });
            sun = new THREE.Mesh(sunGeometry, sunMaterial);
            scene.add(sun);

            // 创建黄道面（土黄色）
            const r_ecliptic = Math.max(a * (1 + e) * 1.2, 2);
            const eclipticGeometry = new THREE.CircleGeometry(r_ecliptic, 64);
            const eclipticMaterial = new THREE.MeshBasicMaterial({
                color: 0xdaa520,
                transparent: true,
                opacity: 0.3,
                side: THREE.DoubleSide
            });
            eclipticPlane = new THREE.Mesh(eclipticGeometry, eclipticMaterial);
            scene.add(eclipticPlane);

            // 添加季节标记
            addSeasonMarkers(r_ecliptic);

            // 创建轨道点
            const points = [];
            const segments = 100;
            for (let j = 0; j <= segments; j++) {
                const theta = (j / segments) * Math.PI * 2;
                const r = a * (1 - e * e) / (1 + e * Math.cos(theta));
                const x = r * Math.cos(theta);
                const y = r * Math.sin(theta);
                const point = new THREE.Vector3(x, y, 0);

                // 应用轨道变换
                point.applyMatrix4(new THREE.Matrix4().makeRotationZ(-omega));
                point.applyMatrix4(new THREE.Matrix4().makeRotationX(i));
                point.applyMatrix4(new THREE.Matrix4().makeRotationZ(-Omega));

                points.push(point);
            }

            // 创建轨道线（黑色）
            const orbitGeometry = new THREE.BufferGeometry().setFromPoints(points);
            const orbitMaterial = new THREE.LineBasicMaterial({ 
                color: 0x000000,
                linewidth: 2
            });
            orbit = new THREE.Line(orbitGeometry, orbitMaterial);
            scene.add(orbit);

            // 添加行星（使用对应的颜色）
            const planetGeometry = new THREE.SphereGeometry(planetSize, 32, 32);
            const planetMaterial = new THREE.MeshBasicMaterial({ 
                color: planetColors[selectedPlanet] || 0x0000ff 
            });
            planet = new THREE.Mesh(planetGeometry, planetMaterial);
            
            // 计算行星位置
            const pos = calculatePosition(a, e, i, L, omega, Omega);
            planet.position.set(pos.x, pos.y, pos.z);
            scene.add(planet);

            // 计算当前位置
            const M = L - omega;
            const E = solveKepler(M, e);
            const nu = 2 * Math.atan(Math.sqrt((1 + e)/(1 - e)) * Math.tan(E/2));
            const r = a * (1 - e * Math.cos(E));
            const x = r * Math.cos(nu);
            const y = r * Math.sin(nu);
            const point = new THREE.Vector3(x, y, 0);
            point.applyMatrix4(new THREE.Matrix4().makeRotationZ(-omega));
            point.applyMatrix4(new THREE.Matrix4().makeRotationX(i));
            point.applyMatrix4(new THREE.Matrix4().makeRotationZ(-Omega));

            // 更新计算过程显示
            let calculation = "";
            
            calculation += "1. 输入轨道根数（J2000历元）：\n";
            calculation += `   半长轴 (a) = ${a.toFixed(8)} AU\n`;
            calculation += `   偏心率 (e) = ${e.toFixed(8)}\n`;
            calculation += `   轨道倾角 (i) = ${(i * 180 / Math.PI).toFixed(8)}°\n`;
            calculation += `   平均经度 (L) = ${(L * 180 / Math.PI).toFixed(8)}°\n`;
            calculation += `   近日点经度 (ω) = ${(omega * 180 / Math.PI).toFixed(8)}°\n`;
            calculation += `   升交点黄经 (Ω) = ${(Omega * 180 / Math.PI).toFixed(8)}°\n\n`;

            calculation += "2. 计算平近点角 (M)：\n";
            calculation += `   M = L - ω\n`;
            calculation += `   M = ${(L * 180 / Math.PI).toFixed(8)}° - ${(omega * 180 / Math.PI).toFixed(8)}°\n`;
            calculation += `   M = ${(M * 180 / Math.PI).toFixed(8)}°\n\n`;

            calculation += "3. 求解开普勒方程：\n";
            calculation += `   E - e·sin(E) = M\n`;
            calculation += `   迭代计算得到：\n`;
            calculation += `   E = ${(E * 180 / Math.PI).toFixed(8)}°\n\n`;

            calculation += "4. 计算真近点角 (ν)：\n";
            calculation += `   ν = 2·arctan(√((1+e)/(1-e))·tan(E/2))\n`;
            calculation += `   ν = ${(nu * 180 / Math.PI).toFixed(8)}°\n\n`;

            calculation += "5. 计算日心距离 (r)：\n";
            calculation += `   r = a(1 - e·cos(E))\n`;
            calculation += `   r = ${r.toFixed(8)} AU\n\n`;

            calculation += "6. 计算轨道平面坐标：\n";
            calculation += `   x' = r·cos(ν) = ${x.toFixed(8)} AU\n`;
            calculation += `   y' = r·sin(ν) = ${y.toFixed(8)} AU\n`;
            calculation += `   z' = 0 AU\n\n`;

            calculation += "7. 坐标变换到黄道平面：\n";
            calculation += "   应用三次旋转变换：\n";
            calculation += "   - 绕z轴旋转(-ω)：近日点经度\n";
            calculation += "   - 绕x轴旋转(-i)：轨道倾角\n";
            calculation += "   - 绕z轴旋转(-Ω)：升交点黄经\n";
            calculation += `   最终坐标：\n`;
            calculation += `   X = ${point.x.toFixed(6)} AU\n`;
            calculation += `   Y = ${point.y.toFixed(6)} AU\n`;
            calculation += `   Z = ${point.z.toFixed(6)} AU\n\n`;

            calculation += "8. 轨道特征：\n";
            calculation += `  - 近日点距离 = ${(a * (1 - e)).toFixed(6)} AU\n`;
            calculation += `  - 远日点距离 = ${(a * (1 + e)).toFixed(6)} AU\n`;
            calculation += `  - 轨道周期 ≈ ${Math.pow(a, 1.5).toFixed(2)} 年\n\n`;

            calculation += "9. 轨道方程：\n";
            calculation += "   极坐标形式：\n";
            calculation += "   r = a(1-e²)/(1 + e·cos(ν))\n\n";
            calculation += "   参数方程形式：\n";
            calculation += "   x = r·[cos(Ω)·cos(ω+ν) - sin(Ω)·sin(ω+ν)·cos(i)]\n";
            calculation += "   y = r·[sin(Ω)·cos(ω+ν) + cos(Ω)·sin(ω+ν)·cos(i)]\n";
            calculation += "   z = r·sin(ω+ν)·sin(i)\n\n";

            calculation += "10. 具体轨道方程（t为时间参数）：\n";
            calculation += "    平近点角随时间变化：\n";
            calculation += `    M(t) = ${(M * 180 / Math.PI).toFixed(6)}° + 360°·t/T\n`;
            calculation += `    其中 T = ${Math.pow(a, 1.5).toFixed(2)} 年\n\n`;

            calculation += "    求解开普勒方程得到偏近点角 E(t)：\n";
            calculation += `    E(t) = M(t) + ${e.toFixed(6)}·sin(E(t))\n\n`;

            calculation += "    真近点角：\n";
            calculation += `    ν(t) = 2·arctan(√(${((1+e)/(1-e)).toFixed(6)})·tan(E(t)/2))\n\n`;

            calculation += "    日心距离：\n";
            calculation += `    r(t) = ${a.toFixed(6)}·(1 - ${e.toFixed(6)}·cos(E(t))) AU\n\n`;

            calculation += "    最终坐标方程：\n";
            const cos_omega = Math.cos(omega);
            const sin_omega = Math.sin(omega);
            const cos_Omega = Math.cos(Omega);
            const sin_Omega = Math.sin(Omega);
            const cos_i = Math.cos(i);
            const sin_i = Math.sin(i);

            calculation += "    X(t) = r(t)·[";
            calculation += `${(cos_Omega*cos_omega - sin_Omega*sin_omega*cos_i).toFixed(6)}·cos(ν(t)) + `;
            calculation += `${(-cos_Omega*sin_omega - sin_Omega*cos_omega*cos_i).toFixed(6)}·sin(ν(t))]\n\n`;

            calculation += "    Y(t) = r(t)·[";
            calculation += `${(sin_Omega*cos_omega + cos_Omega*sin_omega*cos_i).toFixed(6)}·cos(ν(t)) + `;
            calculation += `${(-sin_Omega*sin_omega + cos_Omega*cos_omega*cos_i).toFixed(6)}·sin(ν(t))]\n\n`;

            calculation += "    Z(t) = r(t)·[";
            calculation += `${(sin_omega*sin_i).toFixed(6)}·cos(ν(t)) + `;
            calculation += `${(cos_omega*sin_i).toFixed(6)}·sin(ν(t))]\n`;

            // 将计算过程显示为等宽字体
            document.getElementById('calculation').innerHTML = `<pre style="font-family: Consolas, monospace; font-size: 14px;">${calculation}</pre>`;

            // 调整相机以显示完整轨道
            fitCameraToOrbit(a);
        }

        // 计算位置
        function calculatePosition(a, e, i, L, omega, Omega, time = null) {
            // 计算平近点角
            let M = L - omega;
            
            if (time) {
                // 计算参考时间（J2000.0）到目标时间的年数
                const t_ref = new Date('2000-01-01T12:00:00Z');
                const years = (time - t_ref) / (365.25 * 24 * 60 * 60 * 1000);
                
                // 计算平均角速度（弧度/年）
                const n = 2 * Math.PI / (a ** 1.5);
                
                // 计算目标时间的平近点角
                M = (L - omega + n * years) % (2 * Math.PI);
            }
            
            // 求解开普勒方程
            const E = solveKepler(M, e);
            
            // 计算真近点角
            const nu = 2 * Math.atan(Math.sqrt((1 + e)/(1 - e)) * Math.tan(E/2));
            
            // 计算在轨道平面内的位置
            const r = a * (1 - e * Math.cos(E));
            const x_orbit = r * Math.cos(nu);
            const y_orbit = r * Math.sin(nu);
            
            // 坐标变换矩阵
            const cosOmega = Math.cos(-omega);
            const sinOmega = Math.sin(-omega);
            const cosI = Math.cos(-i);
            const sinI = Math.sin(-i);
            const cosOmegaN = Math.cos(-Omega);
            const sinOmegaN = Math.sin(-Omega);
            
            // 应用坐标变换
            const x = (cosOmegaN * (cosOmega * x_orbit - sinOmega * y_orbit) - 
                      sinOmegaN * (cosI * (sinOmega * x_orbit + cosOmega * y_orbit)));
            const y = (sinOmegaN * (cosOmega * x_orbit - sinOmega * y_orbit) + 
                      cosOmegaN * (cosI * (sinOmega * x_orbit + cosOmega * y_orbit)));
            const z = sinI * (sinOmega * x_orbit + cosOmega * y_orbit);
            
            return new THREE.Vector3(x, y, z);
        }

        // 求解开普勒方程
        function solveKepler(M, e, tolerance = 1e-8) {
            let E = M;
            let delta = 1;
            while (Math.abs(delta) > tolerance) {
                delta = (E - e * Math.sin(E) - M) / (1 - e * Math.cos(E));
                E -= delta;
            }
            return E;
        }

        // 计算速度
        function calculateSpeed() {
            // 获取当前轨道参数
            const a = parseFloat(document.getElementById('semiMajorAxis').value);
            const e = parseFloat(document.getElementById('eccentricity').value);
            const i = parseFloat(document.getElementById('inclination').value);
            const L = parseFloat(document.getElementById('meanLongitude').value);
            const omega = parseFloat(document.getElementById('perihelionLongitude').value);
            const Omega = parseFloat(document.getElementById('ascendingNode').value);

            // 计算轨道周期（开普勒第三定律）
            const period = Math.sqrt(Math.pow(a, 3)) * 365.25; // 转换为地球日
            const days = Math.ceil(period); // 向上取整获取总天数

            const velocities = [];
            const distances = [];
            const dates = [];

            // 计算每一天的速度
            for (let day = 0; day < days; day++) {
                const time = new Date();
                time.setDate(time.getDate() + day);
                
                // 计算该时间点的位置
                const position = calculatePosition(a, e, i, L, omega, Omega, time);
                
                // 计算日心距离（AU）
                const distance = Math.sqrt(
                    position.x * position.x + 
                    position.y * position.y + 
                    position.z * position.z
                );
                
                // 计算轨道速度（km/s）
                // 使用开普勒第二定律计算速度
                const mu = 39.47841760435743; // GM（天文单位^3/年^2）
                const velocity = Math.sqrt(mu * (2/distance - 1/a));
                
                velocities.push(velocity);
                distances.push(distance);
                dates.push(time);
            }

            // 更新计算过程显示
            const calculationDiv = document.getElementById('calculation');
            calculationDiv.innerHTML = `
                <p>轨道周期: ${period.toFixed(2)} 天</p>
                <p>最大速度: ${Math.max(...velocities).toFixed(2)} km/s</p>
                <p>最小速度: ${Math.min(...velocities).toFixed(2)} km/s</p>
                <p>平均速度: ${(velocities.reduce((a, b) => a + b) / velocities.length).toFixed(2)} km/s</p>
            `;

            // 更新速度图表
            updateVelocityChart(velocities, distances, dates);
        }

        // 更新速度图表函数
        function updateVelocityChart(velocities, distances, dates) {
            const ctx = document.getElementById('velocityChart').getContext('2d');
            
            if (window.velocityChart) {
                window.velocityChart.destroy();
            }

            window.velocityChart = new Chart(ctx, {
                type: 'line',
                data: {
                    labels: dates.map(date => date.toLocaleDateString()),
                    datasets: [{
                        label: '轨道速度 (km/s)',
                        data: velocities,
                        borderColor: 'rgb(75, 192, 192)',
                        tension: 0.1
                    }, {
                        label: '日心距离 (AU)',
                        data: distances,
                        borderColor: 'rgb(255, 99, 132)',
                        tension: 0.1,
                        yAxisID: 'y1'
                    }]
                },
                options: {
                    responsive: true,
                    interaction: {
                        mode: 'index',
                        intersect: false,
                    },
                    scales: {
                        y: {
                            type: 'linear',
                            display: true,
                            position: 'left',
                            title: {
                                display: true,
                                text: '速度 (km/s)'
                            }
                        },
                        y1: {
                            type: 'linear',
                            display: true,
                            position: 'right',
                            title: {
                                display: true,
                                text: '距离 (AU)'
                            },
                            grid: {
                                drawOnChartArea: false
                            }
                        }
                    }
                }
            });
        }

        // 初始化速度-距离图表
        function initVelocityChart() {
            const ctx = document.getElementById('velocityChart').getContext('2d');
            velocityChart = new Chart(ctx, {
                type: 'line',
                data: {
                    labels: [],
                    datasets: [{
                        label: '轨道速度 (km/s)',
                        data: [],
                        borderColor: 'rgb(75, 192, 192)',
                        tension: 0.1
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    scales: {
                        x: {
                            title: {
                                display: true,
                                text: '日距 (AU)'
                            }
                        },
                        y: {
                            title: {
                                display: true,
                                text: '速度 (km/s)'
                            }
                        }
                    }
                }
            });
        }

        // 初始化场景
        init();
    </script>
</body>
</html>
