import numpy as np
from scipy.optimize import fsolve

def solve_kepler_second_focus():
    """
    求解满足以下条件的偏心率e：
    1. 行星围绕太阳公转
    2. 行星经过第二焦点时正好是周期的1/4
    3. 使用开普勒方程求解
    
    原理：
    1. 开普勒方程：E - e*sin(E) = M，其中E是偏心角，M是平近点角
    2. 在周期的1/4处，M = π/2
    3. 第二焦点坐标为(2ae,0)，其中a是半长轴
    4. 在椭圆轨道上，点的坐标为：x = a(cos(E) - e), y = a*sqrt(1-e^2)*sin(E)
    5. 当行星经过第二焦点时：x = 2ae, y = 0
    """
    
    def equations(vars):
        """定义方程组"""
        E, e = vars
        
        # 方程1：开普勒方程，在周期1/4处，M = π/2
        eq1 = E - e*np.sin(E) - np.pi/2
        
        # 方程2：行星位于第二焦点(2ae,0)
        # x = a(cos(E) - e) = 2ae
        # 整理得：cos(E) = 3e
        eq2 = np.cos(E) - 3*e
        
        return [eq1, eq2]

    # 初始猜测值
    E_guess = np.pi/2  # 偏心角的初始猜测值
    e_guess = 0.5      # 偏心率的初始猜测值
    
    # 使用fsolve求解方程组
    solution = fsolve(equations, [E_guess, e_guess])
    
    # 返回解得的偏心率e
    return solution[1]

if __name__ == "__main__":
    try:
        e = solve_kepler_second_focus()
        print(f"\n求解结果:")
        print(f"偏心率 e = {e:.8f}")
        
        # 验证结果的准确性
        E = fsolve(lambda x: x - e*np.sin(x) - np.pi/2, np.pi/2)[0]
        print(f"\n验证结果:")
        print(f"偏心角 E = {E:.8f}")
        print(f"M = E - e*sin(E) = {(E - e*np.sin(E)):.8f}")
        print(f"π/2 = {np.pi/2:.8f}")
        print(f"cos(E) = {np.cos(E):.8f}")
        print(f"3e = {3*e:.8f}")
        
        # 计算在该点的实际坐标（假设a=1）
        a = 1
        x = a*(np.cos(E) - e)
        y = a*np.sqrt(1-e**2)*np.sin(E)
        print(f"\n该点坐标（a=1）:")
        print(f"x = {x:.8f}")
        print(f"y = {y:.8f}")
        print(f"第二焦点x坐标(2ae) = {2*a*e:.8f}")
        
    except Exception as ex:
        print(f"计算过程中出现错误: {str(ex)}")
