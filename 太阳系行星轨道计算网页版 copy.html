<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>太阳系轨道计算器</title>
    <style>
        body { margin: 0; }
        #container {
            width: 100%;
            height: 100vh;
            display: flex;
        }
        #controls {
            width: 700px;
            padding: 20px;
            box-sizing: border-box;
            background: #f5f5f5;
            overflow-y: auto;
            display: flex;
            flex-direction: column;
            position: relative;  
        }
        .param-group {
            display: flex;
            margin-bottom: 10px;
            justify-content: space-between;
        }
        .param-item {
            flex: 1;
            margin-right: 10px;
        }
        .param-item:last-child {
            margin-right: 0;
        }
        .param-item label {
            display: block;
            margin-bottom: 5px;
            font-family: "Microsoft YaHei", sans-serif;
            font-size: 14px;
        }
        .param-item input {
            width: 90%;
            padding: 5px;
            font-family: "Microsoft YaHei", sans-serif;
            font-size: 14px;
        }
        #canvas {
            flex-grow: 1;
            display: flex;
            flex-direction: column;
        }
        #orbitView {
            flex: 2;
        }
        #speedChart {
            flex: 1;
            min-height: 300px;
            margin: 10px;
            padding: 10px;
            background: white;
            border-radius: 5px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        #info {
            width: 400px;
            padding: 20px;
            background: #f5f5f5;
            overflow-y: auto;
            font-family: "Microsoft YaHei", sans-serif;
            font-size: 14px;
        }
        .planet-button {
            text-align: left;
            background: #e0e0e0;
            border: none;
            border-radius: 4px;
            margin: 5px;
            padding: 8px 15px;
            cursor: pointer;
        }
        .planet-button:hover {
            background: #d0d0d0;
        }
        .planet-button.active {
            background: #c0c0c0;
        }
        .drag-hint {
            text-align: center;
            color: #666;
            padding: 10px;
            font-size: 12px;
            border-top: 1px solid #ddd;
            margin-top: auto;  
            font-family: "Microsoft YaHei", sans-serif;
        }
        .drag-hint i {
            display: block;
            font-style: normal;
            font-size: 16px;
            margin-bottom: 5px;
            color: #999;
        }
    </style>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/three.js/r128/three.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/dat-gui/0.7.7/dat.gui.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/three@0.128.0/examples/js/controls/OrbitControls.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
</head>
<body>
    <div id="container">
        <div id="controls">
            <h2 style="font-family: 'Microsoft YaHei';">轨道参数</h2>
            <div class="param-group">
                <div class="param-item">
                    <label>半长轴 (AU)</label>
                    <input type="number" id="semiMajorAxis" step="0.00000001" value="1.00000011">
                </div>
                <div class="param-item">
                    <label>偏心率</label>
                    <input type="number" id="eccentricity" step="0.00000001" value="0.01671022">
                </div>
            </div>
            <div class="param-group">
                <div class="param-item">
                    <label>轨道倾角 (度)</label>
                    <input type="number" id="inclination" step="0.00000001" value="0.00005">
                </div>
                <div class="param-item">
                    <label>平均经度 (度)</label>
                    <input type="number" id="meanLongitude" step="0.00000001" value="100.46435">
                </div>
            </div>
            <div class="param-group">
                <div class="param-item">
                    <label>近日点经度 (度)</label>
                    <input type="number" id="perihelionLongitude" step="0.00000001" value="102.94719">
                </div>
                <div class="param-item">
                    <label>升交点黄经 (度)</label>
                    <input type="number" id="ascendingNode" step="0.00000001" value="-11.26064">
                </div>
            </div>
            <div class="param-group">
                <div class="param-item">
                    <label>计算时间</label>
                    <input type="date" id="calculationTime" value="">
                </div>
            </div>
            <div class="param-group">
                <button onclick="updateOrbit()" style="margin-right: 10px;">更新轨道</button>
                <button onclick="calculateSpeed()">计算速度</button>
            </div>
            
            <h2 style="font-family: 'Microsoft YaHei';">太阳系天体</h2>
            <div id="planetButtons"></div>
            
            <!-- 添加拖动提示 -->
            <div class="drag-hint">
                <i>⋮⋮</i>
                中间窗口太阳和轨道 可上下拖动查看更多
            </div>
        </div>
        <div id="canvas">
            <div id="orbitView"></div>
            <div id="speedChart">
                <canvas id="velocityChart"></canvas>
            </div>
        </div>
        <div id="info">
            <h2 style="font-family: 'Microsoft YaHei';">计算过程</h2>
            <div id="calculation"></div>
        </div>
    </div>

    <script>
        // 行星数据
        const planets = {
            "水星": [0.38709927,0.205636,7.004979,252.2503235,77.45779628,48.33076593],
            "金星": [0.72333566,0.006777,3.394676,181.9790995,131.6024672,76.67984255],
            "地球": [1.00000261,0.016711,-0.000015,100.4645717,102.9376819,0],
            "火星": [1.52371034,0.093394,1.849691,-4.55343205,-23.94362959,49.55953891],
            "木星": [5.202887,0.048386,1.304397,34.39644051,14.72847983,100.4739091],
            "土星": [9.53667594,0.053862,2.485992,49.95424423,92.59887831,113.6624245],
            "天王星": [19.18916464,0.047257,0.772638,313.2381045,170.9542763,74.01692503],
            "海王星": [30.06992276,0.00859,1.770043,-55.1200297,44.96476227,131.7842257],
            "冥王星": [39.54,0.24905,17.1405,110.30347,113.834,110.299]
        };

        // 行星颜色配置
        const planetColors = {
            "水星": 0x808080,  // 灰色（类似水星表面）
            "金星": 0xffd700,  // 金色（反映其明亮的大气）
            "地球": 0x4169e1,  // 皇家蓝（代表海洋）
            "火星": 0xff4500,  // 红橙色（火星的红色表面）
            "木星": 0xdeb887,  // 实木色（木星的条纹）
            "土星": 0xf4a460,  // 沙褐色（土星的主要颜色）
            "天王星": 0x40e0d0,  // 绿松石色（天王星的蓝绿色）
            "海王星": 0x0000cd,  // 深蓝色（深海蓝）
            "冥王星": 0x8b4513   // 马鞍棕色（冥王星的褐色）
        };

        // 计算后的行星颜色配置
        const calculatedPlanetColors = {
            "水星": 0xb0b0b0,  // 亮灰色
            "金星": 0xffa500,  // 橙色
            "地球": 0x00bfff,  // 深天蓝
            "火星": 0xff6347,  // 番茄红
            "木星": 0xcd853f,  // 秘鲁色
            "土星": 0xdaa520,  // 金菊黄
            "天王星": 0x48d1cc,  // 中绿宝石
            "海王星": 0x4169e1,  // 皇家蓝
            "冥王星": 0xa0522d   // 赭色
        };

        // Three.js 变量
        let scene, camera, renderer, controls;
        let orbit, sun, aphelion, planet;
        let eclipticPlane, seasonLabels = [];

        // 初始化Three.js场景
        function init() {
            // 创建场景
            scene = new THREE.Scene();
            scene.background = new THREE.Color(0x000000);

            // 创建相机
            camera = new THREE.PerspectiveCamera(75, window.innerWidth / window.innerHeight, 0.1, 1000);
            camera.position.z = 5;

            // 创建渲染器
            renderer = new THREE.WebGLRenderer({ antialias: true });
            renderer.setSize(document.getElementById('orbitView').clientWidth, 
                           document.getElementById('orbitView').clientHeight);
            document.getElementById('orbitView').appendChild(renderer.domElement);

            // 添加轨道控制
            controls = new THREE.OrbitControls(camera, renderer.domElement);
            
            // 创建太阳
            const sunGeometry = new THREE.SphereGeometry(0.1, 32, 32);
            const sunMaterial = new THREE.MeshBasicMaterial({ color: 0xffff00 });
            const sun = new THREE.Mesh(sunGeometry, sunMaterial);
            scene.add(sun);

            // 初始化所有行星的原始轨道
            let originalOrbits = new Map();  // 存储原始轨道
            let calculatedOrbits = new Map(); // 存储计算后的轨道
            for (let planet in planets) {
                const [a, e, i, L, omega, Omega] = planets[planet];
                const orbitGeometry = new THREE.BufferGeometry();
                const points = [];
                const segments = 100;
                
                for (let j = 0; j <= segments; j++) {
                    const theta = (j / segments) * Math.PI * 2;
                    const r = a * (1 - e * e) / (1 + e * Math.cos(theta));
                    const x = r * Math.cos(theta);
                    const y = r * Math.sin(theta);
                    points.push(new THREE.Vector3(x, y, 0));
                }
                
                orbitGeometry.setFromPoints(points);
                const orbitMaterial = new THREE.LineBasicMaterial({ color: planetColors[planet] });
                const orbit = new THREE.Line(orbitGeometry, orbitMaterial);
                
                // 应用轨道倾角和升交点黄经的旋转
                orbit.rotation.x = i * Math.PI / 180;
                orbit.rotation.z = Omega * Math.PI / 180;
                
                scene.add(orbit);
                originalOrbits.set(planet, orbit);
            }

            createPlanetButtons();
            initVelocityChart();
        }

        // 动画循环
        function animate() {
            requestAnimationFrame(animate);
            if (controls) {  
                controls.update();
            }
            renderer.render(scene, camera);
        }

        // 窗口大小调整
        function onWindowResize() {
            if (camera && renderer) {  
                camera.aspect = window.innerWidth / window.innerHeight;
                camera.updateProjectionMatrix();
                renderer.setSize(document.getElementById('orbitView').clientWidth, 
                               document.getElementById('orbitView').clientHeight);
            }
        }

        // 创建文本纹理
        function createTextTexture(text) {
            const canvas = document.createElement('canvas');
            const context = canvas.getContext('2d');
            canvas.width = 512;
            canvas.height = 256;
            
            context.font = 'bold 80px Microsoft YaHei';  // 固定16号字体
            context.fillStyle = '#000000';
            context.textAlign = 'center';
            context.textBaseline = 'middle';
            
            // 清除背景（设为透明）
            context.clearRect(0, 0, canvas.width, canvas.height);
            
            // 绘制文字
            context.fillText(text, 256, 128);
            
            const texture = new THREE.CanvasTexture(canvas);
            texture.needsUpdate = true;
            return texture;
        }

        // 创建行星按钮
        function createPlanetButtons() {
            const container = document.getElementById('planetButtons');
            for (let planet in planets) {
                const button = document.createElement('button');
                button.className = 'planet-button';
                button.textContent = planet;
                button.onclick = () => setPlanetParams(planet);
                if (planet === "地球") {
                    button.classList.add('active');
                }
                container.appendChild(button);
            }
        }

        // 设置行星参数
        function setPlanetParams(planet) {
            // 移除所有按钮的active类
            const buttons = document.getElementsByTagName('button');
            for (let button of buttons) {
                button.classList.remove('active');
                if (button.textContent === planet) {
                    button.classList.add('active');
                }
            }

            // 设置参数
            const params = planets[planet];
            document.getElementById('semiMajorAxis').value = params[0];
            document.getElementById('eccentricity').value = params[1];
            document.getElementById('inclination').value = params[2];
            document.getElementById('meanLongitude').value = params[3];
            document.getElementById('perihelionLongitude').value = params[4];
            document.getElementById('ascendingNode').value = params[5];

            // 更新轨道
            updateOrbit();
        }

        // 添加季节标记
        function addSeasonMarkers(r_ecliptic) {
            const seasons = {
                "春分 (0°)": new THREE.Vector3(r_ecliptic, 0, 0),
                "夏至 (90°)": new THREE.Vector3(0, r_ecliptic, 0),
                "秋分 (180°)": new THREE.Vector3(-r_ecliptic, 0, 0),
                "冬至 (270°)": new THREE.Vector3(0, -r_ecliptic, 0)
            };

            const spriteScale = r_ecliptic * 0.3;  // 调整标签大小
            for (const [text, position] of Object.entries(seasons)) {
                const sprite = new THREE.Sprite(
                    new THREE.SpriteMaterial({
                        map: createTextTexture(text),
                        transparent: true  // 启用透明
                    })
                );
                sprite.position.copy(position);
                sprite.scale.set(spriteScale, spriteScale * 0.5, 1);
                scene.add(sprite);
                seasonLabels.push(sprite);
            }
        }

        // 设置相机以显示完整轨道
        function fitCameraToOrbit(a) {
            if (camera && controls) {
                const distance = Math.max(a * 2.5, 5);
                camera.position.set(0, 0, distance);  
                controls.target.set(0, 0, 0);
                camera.lookAt(0, 0, 0);
                camera.updateProjectionMatrix();
                controls.update();
            }
        }

        // 计算屏幕固定大小
        function getScreenConstantSize() {
            const vFOV = camera.fov * Math.PI / 180;  
            const height = 2 * Math.tan(vFOV / 2);  
            return height * 0.05;  
        }

        // 更新轨道
        function updateOrbit() {
            // 获取输入参数
            const a = parseFloat(document.getElementById('semiMajorAxis').value);
            const e = parseFloat(document.getElementById('eccentricity').value);
            const i = parseFloat(document.getElementById('inclination').value);
            const L = parseFloat(document.getElementById('meanLongitude').value);
            const omega = parseFloat(document.getElementById('perihelionLongitude').value);
            const Omega = parseFloat(document.getElementById('ascendingNode').value);
            
            // 获取当前选中的行星
            const activePlanetButton = document.querySelector('.planet-button.active');
            if (!activePlanetButton) {
                alert('请先选择一个行星！');
                return;
            }
            const planetName = activePlanetButton.textContent;

            // 移除之前计算的轨道（如果存在）
            if (calculatedOrbits.has(planetName)) {
                scene.remove(calculatedOrbits.get(planetName));
            }

            // 创建新的轨道
            const orbitGeometry = new THREE.BufferGeometry();
            const points = [];
            const segments = 100;
            
            for (let j = 0; j <= segments; j++) {
                const theta = (j / segments) * Math.PI * 2;
                const r = a * (1 - e * e) / (1 + e * Math.cos(theta));
                const x = r * Math.cos(theta);
                const y = r * Math.sin(theta);
                points.push(new THREE.Vector3(x, y, 0));
            }
            
            orbitGeometry.setFromPoints(points);
            const orbitMaterial = new THREE.LineBasicMaterial({ color: calculatedPlanetColors[planetName] });
            const orbit = new THREE.Line(orbitGeometry, orbitMaterial);
            
            // 应用轨道倾角和升交点黄经的旋转
            orbit.rotation.x = i * Math.PI / 180;
            orbit.rotation.z = Omega * Math.PI / 180;
            
            scene.add(orbit);
            calculatedOrbits.set(planetName, orbit);

            // 更新相机视角以适应新轨道
            fitCameraToOrbit(Math.max(a, 1));

            // 更新速度图表
            updateVelocityChart(a, e);
            
            // 计算并显示位置信息
            const time = document.getElementById('calculationTime').value;
            const position = calculatePosition(a, e, i, L, omega, Omega, time ? new Date(time) : null);
            
            // 在info区域显示计算结果
            document.getElementById('calculation').innerHTML = `
                <p>计算结果：</p>
                <p>距离太阳：${position.r.toFixed(8)} AU</p>
                <p>真近点角：${position.v.toFixed(8)}°</p>
                <p>位置：</p>
                <p>X: ${position.x.toFixed(8)} AU</p>
                <p>Y: ${position.y.toFixed(8)} AU</p>
                <p>Z: ${position.z.toFixed(8)} AU</p>
            `;
        }

        // 计算位置
        function calculatePosition(a, e, i, L, omega, Omega, time = null) {
            // 计算平近点角
            let M = L - omega;
            
            if (time) {
                // 计算参考时间（J2000.0）到目标时间的年数
                const t_ref = new Date('2000-01-01T12:00:00Z');
                const years = (time - t_ref) / (365.25 * 24 * 60 * 60 * 1000);
                
                // 计算平均角速度（弧度/年）
                const n = 2 * Math.PI / (a ** 1.5);
                
                // 计算目标时间的平近点角
                M = (L - omega + n * years) % (2 * Math.PI);
            }
            
            // 求解开普勒方程
            const E = solveKepler(M, e);
            
            // 计算真近点角
            const nu = 2 * Math.atan(Math.sqrt((1 + e)/(1 - e)) * Math.tan(E/2));
            
            // 计算在轨道平面内的位置
            const r = a * (1 - e * Math.cos(E));
            const x_orbit = r * Math.cos(nu);
            const y_orbit = r * Math.sin(nu);
            
            // 坐标变换矩阵
            const cosOmega = Math.cos(-omega);
            const sinOmega = Math.sin(-omega);
            const cosI = Math.cos(-i * Math.PI / 180);
            const sinI = Math.sin(-i * Math.PI / 180);
            const cosOmegaN = Math.cos(-Omega * Math.PI / 180);
            const sinOmegaN = Math.sin(-Omega * Math.PI / 180);
            
            // 应用坐标变换
            const x = (cosOmegaN * (cosOmega * x_orbit - sinOmega * y_orbit) - 
                      sinOmegaN * (cosI * (sinOmega * x_orbit + cosOmega * y_orbit)));
            const y = (sinOmegaN * (cosOmega * x_orbit - sinOmega * y_orbit) + 
                      cosOmegaN * (cosI * (sinOmega * x_orbit + cosOmega * y_orbit)));
            const z = sinI * (sinOmega * x_orbit + cosOmega * y_orbit);
            
            return { x, y, z, r, v: nu * 180 / Math.PI };
        }

        // 求解开普勒方程
        function solveKepler(M, e, tolerance = 1e-8) {
            let E = M;
            let delta = 1;
            while (Math.abs(delta) > tolerance) {
                delta = (E - e * Math.sin(E) - M) / (1 - e * Math.cos(E));
                E -= delta;
            }
            return E;
        }

        // 计算速度
        function calculateSpeed() {
            // 获取参数
            const a = parseFloat(document.getElementById('semiMajorAxis').value);
            const e = parseFloat(document.getElementById('eccentricity').value);
            const i = parseFloat(document.getElementById('inclination').value) * Math.PI / 180;
            const L = parseFloat(document.getElementById('meanLongitude').value) * Math.PI / 180;
            const omega = parseFloat(document.getElementById('perihelionLongitude').value) * Math.PI / 180;
            const Omega = parseFloat(document.getElementById('ascendingNode').value) * Math.PI / 180;
            
            // 获取时间
            const timeStr = document.getElementById('calculationTime').value;
            const time = timeStr ? new Date(timeStr) : new Date();
            
            // 计算位置
            const pos = calculatePosition(a, e, i, L, omega, Omega, time);
            
            // 计算当前距离太阳的距离（AU）
            const r = Math.sqrt(pos.x * pos.x + pos.y * pos.y + pos.z * pos.z);
            
            // 太阳引力常数（AU³/年²）
            const GM_sun = 4 * Math.PI * Math.PI;
            
            // 计算轨道速度（AU/年）
            const v = Math.sqrt(GM_sun * (2/r - 1/a));
            
            // 计算近日点和远日点距离
            const r_perihelion = a * (1 - e);
            const r_aphelion = a * (1 + e);
            
            // 计算近日点和远日点速度
            const v_perihelion = Math.sqrt(GM_sun * (2/r_perihelion - 1/a));
            const v_aphelion = Math.sqrt(GM_sun * (2/r_aphelion - 1/a));
            
            // 转换为km/s
            const AU_to_km = 149597870.7;
            const year_to_sec = 365.25 * 24 * 3600;
            const v_kms = v * AU_to_km / year_to_sec;
            const v_perihelion_kms = v_perihelion * AU_to_km / year_to_sec;
            const v_aphelion_kms = v_aphelion * AU_to_km / year_to_sec;
            
            // 更新行星位置（蓝色）
            if (planet) scene.remove(planet);
            const planetSize = Math.max(a * 0.1 * 0.3, 0.05);
            const planetGeometry = new THREE.SphereGeometry(planetSize, 32, 32);
            const planetMaterial = new THREE.MeshBasicMaterial({ color: 0x0000ff });
            planet = new THREE.Mesh(planetGeometry, planetMaterial);
            planet.position.copy(pos);
            scene.add(planet);
            
            // 更新计算过程文本
            const calculation = document.getElementById('calculation');
            calculation.innerHTML = `
                <h3>计算时间：${timeStr || new Date().toISOString().split('T')[0]}</h3>
                <h3>1. 轨道参数：</h3>
                <p>- 半长轴 = ${a.toFixed(3)} AU</p>
                <p>- 偏心率 = ${e.toFixed(6)}</p>
                <p>- 轨道周期 = ${(a ** 1.5).toFixed(2)} 年</p>
                <h3>2. 距离：</h3>
                <p>- 当前日距 = ${r.toFixed(3)} AU</p>
                <p>- 近日点距离 = ${r_perihelion.toFixed(3)} AU</p>
                <p>- 远日点距离 = ${r_aphelion.toFixed(3)} AU</p>
                <h3>3. 轨道速度：</h3>
                <p>- 当前速度 = ${v_kms.toFixed(2)} km/s</p>
                <p>- 近日点速度 = ${v_perihelion_kms.toFixed(2)} km/s</p>
                <p>- 远日点速度 = ${v_aphelion_kms.toFixed(2)} km/s</p>
            `;
            
            // 更新速度-距离图表
            updateVelocityChart(a, e);
        }

        // 初始化速度-距离图表
        function initVelocityChart() {
            const ctx = document.getElementById('velocityChart').getContext('2d');
            velocityChart = new Chart(ctx, {
                type: 'line',
                data: {
                    labels: [],
                    datasets: [{
                        label: '轨道速度 (km/s)',
                        data: [],
                        borderColor: 'rgb(75, 192, 192)',
                        tension: 0.1
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    scales: {
                        x: {
                            title: {
                                display: true,
                                text: '日距 (AU)'
                            }
                        },
                        y: {
                            title: {
                                display: true,
                                text: '速度 (km/s)'
                            }
                        }
                    }
                }
            });
        }

        // 更新速度-距离图表
        function updateVelocityChart(a, e) {
            // 生成距离数据点
            const r_perihelion = a * (1 - e);
            const r_aphelion = a * (1 + e);
            const points = 100;
            const distances = [];
            const velocities = [];
            
            // 太阳引力常数（AU³/年²）
            const GM_sun = 4 * Math.PI * Math.PI;
            
            // 计算不同距离下的速度
            for (let i = 0; i < points; i++) {
                const r = r_perihelion + (r_aphelion - r_perihelion) * i / (points - 1);
                const v = Math.sqrt(GM_sun * (2/r - 1/a));
                
                // 转换为km/s
                const AU_to_km = 149597870.7;
                const year_to_sec = 365.25 * 24 * 3600;
                const v_kms = v * AU_to_km / year_to_sec;
                
                distances.push(r.toFixed(3));
                velocities.push(v_kms);
            }
            
            // 更新图表数据
            velocityChart.data.labels = distances;
            velocityChart.data.datasets[0].data = velocities;
            velocityChart.update();
        }

        // 初始化场景
        init();

        // 开始动画循环
        animate();

        // 添加窗口大小调整监听
        window.addEventListener('resize', onWindowResize, false);
    </script>
</body>
</html>
