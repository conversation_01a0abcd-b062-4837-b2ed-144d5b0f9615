#!/usr/bin/env python3
"""
分析地球轨道数据中的开普勒定律适用性
比较二维(X-Y)和三维(X-Y-Z)分析的差异
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

def analyze_kepler_laws():
    """分析开普勒定律在地球轨道数据中的表现"""
    
    # 读取数据
    df = pd.read_csv('planet_data/地球_2024_处理后数据.csv')
    
    print("开普勒定律分析")
    print("=" * 50)
    
    # 计算距离
    r_2d = np.sqrt(df['x_position_au']**2 + df['y_position_au']**2)  # 二维距离
    r_3d = np.sqrt(df['x_position_au']**2 + df['y_position_au']**2 + df['z_position_au']**2)  # 三维距离
    
    # 计算速度
    v_2d = np.sqrt(df['x_velocity_au_day']**2 + df['y_velocity_au_day']**2)  # 二维速度
    v_3d = np.sqrt(df['x_velocity_au_day']**2 + df['y_velocity_au_day']**2 + df['z_velocity_au_day']**2)  # 三维速度
    
    print("1. 距离分析")
    print("-" * 30)
    print(f"二维距离 (AU): {r_2d.min():.6f} - {r_2d.max():.6f}")
    print(f"三维距离 (AU): {r_3d.min():.6f} - {r_3d.max():.6f}")
    print(f"JPL距离 (AU): {df['distance_au'].min():.6f} - {df['distance_au'].max():.6f}")
    
    # 比较差异
    diff_2d_3d = np.abs(r_3d - r_2d)
    diff_3d_jpl = np.abs(r_3d - df['distance_au'])
    
    print(f"\n距离差异:")
    print(f"3D vs 2D 最大差异: {diff_2d_3d.max():.8f} AU ({diff_2d_3d.max()*149597870.7:.1f} km)")
    print(f"3D vs JPL 最大差异: {diff_3d_jpl.max():.8f} AU ({diff_3d_jpl.max()*149597870.7:.1f} km)")
    
    print("\n2. 速度分析")
    print("-" * 30)
    print(f"二维速度 (AU/day): {v_2d.min():.6f} - {v_2d.max():.6f}")
    print(f"三维速度 (AU/day): {v_3d.min():.6f} - {v_3d.max():.6f}")
    
    # 转换为km/s
    au_day_to_km_s = 1731.46
    print(f"二维速度 (km/s): {v_2d.min()*au_day_to_km_s:.2f} - {v_2d.max()*au_day_to_km_s:.2f}")
    print(f"三维速度 (km/s): {v_3d.min()*au_day_to_km_s:.2f} - {v_3d.max()*au_day_to_km_s:.2f}")
    print(f"JPL速度 (km/s): {df['speed_km_s'].min():.2f} - {df['speed_km_s'].max():.2f}")
    
    print("\n3. 开普勒第二定律验证 (面积速度)")
    print("-" * 40)
    
    # 计算角动量 (面积速度的2倍)
    # L = r × v (叉积的模长)
    
    # 二维情况
    L_2d = r_2d * v_2d  # 简化计算，假设垂直
    
    # 三维情况 - 计算角动量矢量
    # L = r × v
    Lx = df['y_position_au'] * df['z_velocity_au_day'] - df['z_position_au'] * df['y_velocity_au_day']
    Ly = df['z_position_au'] * df['x_velocity_au_day'] - df['x_position_au'] * df['z_velocity_au_day']
    Lz = df['x_position_au'] * df['y_velocity_au_day'] - df['y_position_au'] * df['x_velocity_au_day']
    
    L_3d = np.sqrt(Lx**2 + Ly**2 + Lz**2)
    
    print(f"角动量变化 (二维): {(L_2d.max() - L_2d.min())/L_2d.mean()*100:.4f}%")
    print(f"角动量变化 (三维): {(L_3d.max() - L_3d.min())/L_3d.mean()*100:.4f}%")
    
    # 理想情况下角动量应该守恒（变化为0）
    print(f"角动量标准差 (二维): {L_2d.std()/L_2d.mean()*100:.4f}%")
    print(f"角动量标准差 (三维): {L_3d.std()/L_3d.mean()*100:.4f}%")
    
    print("\n4. 开普勒第三定律验证")
    print("-" * 30)
    
    # T² ∝ a³, 其中T是周期，a是半长轴
    # 对于地球，T = 1年，a ≈ 1 AU
    
    # 计算平均距离作为半长轴的近似
    a_2d = r_2d.mean()
    a_3d = r_3d.mean()
    
    print(f"半长轴 (二维): {a_2d:.6f} AU")
    print(f"半长轴 (三维): {a_3d:.6f} AU")
    print(f"理论值: 1.000000 AU")
    
    print("\n5. Z坐标的影响评估")
    print("-" * 30)
    
    z_impact_distance = (r_3d - r_2d) / r_2d * 100
    z_impact_speed = (v_3d - v_2d) / v_2d * 100
    
    print(f"Z坐标对距离的影响: {z_impact_distance.mean():.6f}% (最大: {z_impact_distance.max():.6f}%)")
    print(f"Z坐标对速度的影响: {z_impact_speed.mean():.6f}% (最大: {z_impact_speed.max():.6f}%)")
    
    print("\n6. 结论")
    print("-" * 20)
    print("• Z坐标虽然存在，但影响极小（< 0.03%）")
    print("• 对于大多数应用，二维分析已足够精确")
    print("• 高精度计算需要考虑三维效应")
    print("• 开普勒定律在三维空间中仍然适用")
    
    # 创建可视化
    create_comparison_plots(df, r_2d, r_3d, v_2d, v_3d, L_2d, L_3d)

def create_comparison_plots(df, r_2d, r_3d, v_2d, v_3d, L_2d, L_3d):
    """创建比较图表"""
    
    fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(15, 12))
    
    days = range(len(df))
    
    # 1. 距离比较
    ax1.plot(days, r_2d, 'b-', label='二维距离', alpha=0.7)
    ax1.plot(days, r_3d, 'r-', label='三维距离', alpha=0.7)
    ax1.plot(days, df['distance_au'], 'g--', label='JPL距离', alpha=0.5)
    ax1.set_xlabel('天数')
    ax1.set_ylabel('距离 (AU)')
    ax1.set_title('距离比较：二维 vs 三维')
    ax1.legend()
    ax1.grid(True, alpha=0.3)
    
    # 2. 速度比较
    au_day_to_km_s = 1731.46
    ax2.plot(days, v_2d * au_day_to_km_s, 'b-', label='二维速度', alpha=0.7)
    ax2.plot(days, v_3d * au_day_to_km_s, 'r-', label='三维速度', alpha=0.7)
    ax2.plot(days, df['speed_km_s'], 'g--', label='JPL速度', alpha=0.5)
    ax2.set_xlabel('天数')
    ax2.set_ylabel('速度 (km/s)')
    ax2.set_title('速度比较：二维 vs 三维')
    ax2.legend()
    ax2.grid(True, alpha=0.3)
    
    # 3. Z坐标变化
    ax3.plot(days, df['z_position_au'] * 149597870.7, 'purple', linewidth=1)
    ax3.set_xlabel('天数')
    ax3.set_ylabel('Z坐标 (km)')
    ax3.set_title('Z坐标随时间变化')
    ax3.grid(True, alpha=0.3)
    
    # 4. 角动量比较
    ax4.plot(days, L_2d, 'b-', label='二维角动量', alpha=0.7)
    ax4.plot(days, L_3d, 'r-', label='三维角动量', alpha=0.7)
    ax4.set_xlabel('天数')
    ax4.set_ylabel('角动量 (AU²/day)')
    ax4.set_title('角动量守恒验证')
    ax4.legend()
    ax4.grid(True, alpha=0.3)
    
    plt.tight_layout()
    plt.savefig('planet_data/开普勒定律分析图表.png', dpi=300, bbox_inches='tight')
    print(f"\n✓ 分析图表已保存到: planet_data/开普勒定律分析图表.png")
    plt.show()

if __name__ == "__main__":
    analyze_kepler_laws()
