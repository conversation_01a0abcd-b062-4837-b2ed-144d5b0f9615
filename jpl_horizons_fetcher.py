#!/usr/bin/env python3
"""
JPL HORIZONS 太阳系行星数据获取器
使用NASA JPL HORIZONS系统获取高精度的行星位置和速度数据

作者: 太阳系轨道计算项目
日期: 2024
"""

import os
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from astroquery.jplhorizons import Horizons
from astropy.time import Time
import warnings
import ssl
import urllib3

# 忽略一些不重要的警告
warnings.filterwarnings('ignore', category=UserWarning)
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

# 处理SSL问题
ssl._create_default_https_context = ssl._create_unverified_context

class JPLHorizonsDataFetcher:
    """JPL HORIZONS数据获取器"""
    
    def __init__(self):
        """初始化数据获取器"""
        # 太阳系主要天体的JPL HORIZONS ID
        self.planet_ids = {
            '水星': '199',    # Mercury
            '金星': '299',    # Venus  
            '地球': '399',    # Earth
            '火星': '499',    # Mars
            '木星': '599',    # Jupiter
            '土星': '699',    # Saturn
            '天王星': '799',  # Uranus
            '海王星': '899',  # Neptune
            '冥王星': '999',  # Pluto
            '月球': '301',    # Moon
            '太阳': '10'      # Sun
        }
        
        # 创建输出目录
        self.output_dir = 'planet_data'
        if not os.path.exists(self.output_dir):
            os.makedirs(self.output_dir)
            print(f"创建输出目录: {self.output_dir}")
    
    def get_planet_data(self, planet_name, year=2023, location='@sun'):
        """
        获取指定行星在指定年份的每日数据
        
        参数:
        planet_name: 行星名称（中文）
        year: 年份
        location: 观测位置，默认'@sun'表示日心坐标系
        
        返回:
        pandas.DataFrame: 包含位置和速度数据的DataFrame
        """
        if planet_name not in self.planet_ids:
            raise ValueError(f"不支持的行星: {planet_name}")
        
        planet_id = self.planet_ids[planet_name]
        
        # 设置时间范围（整年）
        start_time = f"{year}-01-01"
        end_time = f"{year}-12-31"
        
        print(f"正在获取 {planet_name} 在 {year} 年的数据...")
        print(f"JPL HORIZONS ID: {planet_id}")
        print(f"时间范围: {start_time} 到 {end_time}")
        print(f"参考系: {location}")
        
        try:
            # 创建Horizons查询对象
            obj = Horizons(
                id=planet_id,
                location=location,
                epochs={'start': start_time, 'stop': end_time, 'step': '1d'}
            )
            
            # 获取矢量数据（位置和速度）
            print("正在查询JPL HORIZONS系统...")
            vectors = obj.vectors()
            
            # 转换为DataFrame
            df = vectors.to_pandas()
            
            # 选择和重命名列
            columns_mapping = {
                'datetime_jd': 'julian_date',
                'datetime_str': 'date',
                'x': 'x_position_au',
                'y': 'y_position_au', 
                'z': 'z_position_au',
                'vx': 'x_velocity_au_day',
                'vy': 'y_velocity_au_day',
                'vz': 'z_velocity_au_day',
                'range': 'distance_au',
                'range_rate': 'range_rate_au_day'
            }
            
            # 选择需要的列并重命名
            available_columns = [col for col in columns_mapping.keys() if col in df.columns]
            df_selected = df[available_columns].copy()
            df_selected.rename(columns=columns_mapping, inplace=True)
            
            # 计算轨道速度（km/s）
            if all(col in df_selected.columns for col in ['x_velocity_au_day', 'y_velocity_au_day', 'z_velocity_au_day']):
                # 1 AU/day = 1731.46 km/s
                au_day_to_km_s = 1731.46
                df_selected['speed_km_s'] = np.sqrt(
                    df_selected['x_velocity_au_day']**2 + 
                    df_selected['y_velocity_au_day']**2 + 
                    df_selected['z_velocity_au_day']**2
                ) * au_day_to_km_s
            
            print(f"成功获取 {len(df_selected)} 天的数据")
            return df_selected
            
        except Exception as e:
            print(f"获取数据时发生错误: {e}")
            return None
    
    def save_to_csv(self, data, planet_name, year):
        """
        将数据保存为CSV文件
        
        参数:
        data: pandas.DataFrame
        planet_name: 行星名称
        year: 年份
        """
        if data is None:
            print("数据为空，无法保存")
            return
        
        filename = f"{planet_name}_{year}_data.csv"
        filepath = os.path.join(self.output_dir, filename)
        
        # 保存CSV文件
        data.to_csv(filepath, index=False, encoding='utf-8-sig')
        print(f"数据已保存到: {filepath}")
        
        # 显示数据摘要
        print(f"\n数据摘要:")
        print(f"总天数: {len(data)}")
        print(f"数据列: {list(data.columns)}")
        
        if 'distance_au' in data.columns:
            print(f"平均距离: {data['distance_au'].mean():.6f} AU")
            print(f"最近距离: {data['distance_au'].min():.6f} AU")
            print(f"最远距离: {data['distance_au'].max():.6f} AU")
        
        if 'speed_km_s' in data.columns:
            print(f"平均速度: {data['speed_km_s'].mean():.2f} km/s")
            print(f"最高速度: {data['speed_km_s'].max():.2f} km/s")
            print(f"最低速度: {data['speed_km_s'].min():.2f} km/s")
    
    def get_all_planets_data(self, year=2023):
        """
        获取所有主要行星的数据
        
        参数:
        year: 年份
        """
        print(f"开始获取 {year} 年所有主要行星的数据...")
        print("=" * 60)
        
        # 排除太阳和月球，只获取行星数据
        planets_to_fetch = ['水星', '金星', '地球', '火星', '木星', '土星', '天王星', '海王星', '冥王星']
        
        for planet in planets_to_fetch:
            print(f"\n处理 {planet}...")
            data = self.get_planet_data(planet, year)
            if data is not None:
                self.save_to_csv(data, planet, year)
            print("-" * 40)
        
        print(f"\n所有数据获取完成！文件保存在 {self.output_dir} 目录中")

def main():
    """主函数"""
    print("JPL HORIZONS 太阳系行星数据获取器")
    print("=" * 50)
    print("使用NASA JPL HORIZONS系统获取高精度行星数据")
    print("数据包括：位置坐标、速度矢量、距离、轨道速度等")
    print("坐标系：日心黄道坐标系（太阳系质心参考系）")
    print("=" * 50)
    
    # 创建数据获取器
    fetcher = JPLHorizonsDataFetcher()
    
    # 获取2023年所有行星数据
    fetcher.get_all_planets_data(2023)

if __name__ == "__main__":
    main()
