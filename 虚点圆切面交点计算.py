#!/usr/bin/env python3
"""
基于JPL HORIZONS地球轨道数据的虚点圆射线交点计算

计算步骤：
1. 从JPL数据计算轨道参数（偏心率e，半长轴a）
2. 计算虚点值：a(1-e²)/2
3. 计算虚半径：2a - a(1-e²)/2
4. 虚点位置：(0, -a(1-e²)/2, 0)
5. 以虚点为圆心，虚半径为半径做圆
6. 以太阳为顶点，通过每日地球坐标做切面
7. 求切面与虚点圆的交点
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from mpl_toolkits.mplot3d import Axes3D
import os

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

def load_earth_orbit_data(file_path='planet_data/地球_2024_1天_处理后数据.csv'):
    """加载地球轨道数据"""
    
    if not os.path.exists(file_path):
        print(f"❌ 数据文件不存在: {file_path}")
        print("请先运行数据处理脚本生成轨道数据")
        return None
    
    try:
        df = pd.read_csv(file_path)
        print(f"✓ 成功加载轨道数据，共 {len(df)} 个数据点")
        return df
    except Exception as e:
        print(f"❌ 加载数据失败: {e}")
        return None

def calculate_orbital_parameters(df):
    """从轨道数据计算轨道参数"""
    
    if df is None:
        return None
    
    # 计算距离（如果没有distance_au列）
    if 'distance_au' not in df.columns:
        df['distance_au'] = np.sqrt(
            df['x_position_au']**2 + 
            df['y_position_au']**2 + 
            df['z_position_au']**2
        )
    
    # 计算轨道参数
    r_min = df['distance_au'].min()  # 近日点距离
    r_max = df['distance_au'].max()  # 远日点距离
    
    # 半长轴 a = (r_max + r_min) / 2
    a = (r_max + r_min) / 2
    
    # 偏心率 e = (r_max - r_min) / (r_max + r_min)
    e = (r_max - r_min) / (r_max + r_min)
    
    # 半短轴 b = a * sqrt(1 - e²)
    b = a * np.sqrt(1 - e**2)
    
    print(f"\n轨道参数计算结果:")
    print(f"半长轴 a = {a:.6f} AU ({a * 149597870.7:.0f} km)")
    print(f"偏心率 e = {e:.6f}")
    print(f"半短轴 b = {b:.6f} AU ({b * 149597870.7:.0f} km)")
    print(f"近日点距离 = {r_min:.6f} AU")
    print(f"远日点距离 = {r_max:.6f} AU")
    
    return {
        'a': a,
        'e': e,
        'b': b,
        'r_min': r_min,
        'r_max': r_max
    }

def calculate_virtual_point_parameters(orbital_params):
    """计算虚点相关参数"""

    a = orbital_params['a']
    e = orbital_params['e']

    # 虚点值：2ae
    virtual_point_value = 2 * a * e

    # 虚半径：2a - 2ae = 2a(1-e)
    virtual_radius = 2 * a - virtual_point_value

    # 虚点坐标：(0, -2ae, 0)
    virtual_point = np.array([0, -virtual_point_value, 0])

    print(f"\n虚点参数计算结果:")
    print(f"虚点值 2ae = {virtual_point_value:.6f} AU ({virtual_point_value * 149597870.7:.0f} km)")
    print(f"虚半径 2a - 2ae = 2a(1-e) = {virtual_radius:.6f} AU ({virtual_radius * 149597870.7:.0f} km)")
    print(f"虚点坐标 = ({virtual_point[0]:.6f}, {virtual_point[1]:.6f}, {virtual_point[2]:.6f}) AU")

    return {
        'virtual_point_value': virtual_point_value,
        'virtual_radius': virtual_radius,
        'virtual_point': virtual_point
    }

def calculate_ray_circle_intersection(ray_origin, ray_direction, circle_center, circle_radius):
    """
    计算射线与圆的交点（在XY平面内）

    参数:
    ray_origin: 射线起点 (太阳位置)
    ray_direction: 射线方向 (指向地球XY坐标)
    circle_center: 圆心 (虚点)
    circle_radius: 圆半径

    返回:
    交点列表 (在XY平面内，Z=0)
    """

    # 确保在XY平面内计算（Z=0）
    ray_origin_2d = np.array([ray_origin[0], ray_origin[1]])
    ray_direction_2d = np.array([ray_direction[0], ray_direction[1]])
    circle_center_2d = np.array([circle_center[0], circle_center[1]])

    # 标准化射线方向
    ray_direction_2d = ray_direction_2d / np.linalg.norm(ray_direction_2d)

    # 射线方程: P = ray_origin_2d + t * ray_direction_2d
    # 圆方程: |P - circle_center_2d|² = circle_radius²
    #
    # 代入得到: |ray_origin_2d + t * ray_direction_2d - circle_center_2d|² = circle_radius²

    # 设 oc = ray_origin_2d - circle_center_2d
    oc = ray_origin_2d - circle_center_2d

    # 展开得到二次方程: a*t² + b*t + c = 0
    a = np.dot(ray_direction_2d, ray_direction_2d)  # 应该等于1（已标准化）
    b = 2.0 * np.dot(oc, ray_direction_2d)
    c = np.dot(oc, oc) - circle_radius**2

    # 计算判别式
    discriminant = b**2 - 4*a*c

    if discriminant < 0:
        # 无交点
        return []
    elif discriminant == 0:
        # 一个交点（相切）
        t = -b / (2*a)
        if t >= 0:  # 射线方向
            intersection_2d = ray_origin_2d + t * ray_direction_2d
            intersection_3d = np.array([intersection_2d[0], intersection_2d[1], 0])
            return [intersection_3d]
        else:
            return []
    else:
        # 两个交点
        sqrt_discriminant = np.sqrt(discriminant)
        t1 = (-b - sqrt_discriminant) / (2*a)
        t2 = (-b + sqrt_discriminant) / (2*a)

        intersections = []

        # 只考虑射线方向的交点（t >= 0）
        if t1 >= 0:
            intersection_2d = ray_origin_2d + t1 * ray_direction_2d
            intersection_3d = np.array([intersection_2d[0], intersection_2d[1], 0])
            intersections.append(intersection_3d)

        if t2 >= 0:
            intersection_2d = ray_origin_2d + t2 * ray_direction_2d
            intersection_3d = np.array([intersection_2d[0], intersection_2d[1], 0])
            intersections.append(intersection_3d)

        return intersections

def calculate_ray_circle_intersections(df, virtual_params):
    """计算射线与虚点圆的交点"""

    virtual_point = virtual_params['virtual_point']
    virtual_radius = virtual_params['virtual_radius']

    # 太阳位置（原点）
    sun_position = np.array([0, 0, 0])

    # 虚点圆在XY平面内（Z = 0）
    circle_center = virtual_point  # 虚点坐标 (0, -2ae, 0)

    intersections_data = []

    print(f"\n开始计算 {len(df)} 条射线与虚点圆的交点...")
    print(f"虚点圆: 圆心 = ({circle_center[0]:.6f}, {circle_center[1]:.6f}, {circle_center[2]:.6f}), 半径 = {virtual_radius:.6f} AU")
    print(f"射线: 从太阳 (0, 0, 0) 指向每日地球的 (x, y) 坐标")

    for i, row in df.iterrows():
        # 地球位置
        earth_position = np.array([
            row['x_position_au'],
            row['y_position_au'],
            row['z_position_au']
        ])

        # 射线方向：从太阳指向地球的XY坐标
        ray_direction = np.array([earth_position[0], earth_position[1], 0])  # 只取XY分量

        # 如果射线方向为零向量，跳过
        if np.linalg.norm(ray_direction) < 1e-10:
            intersections = []
        else:
            # 计算射线与虚点圆的交点
            intersections = calculate_ray_circle_intersection(
                sun_position, ray_direction, circle_center, virtual_radius
            )

        # 记录结果
        date = row.get('date', f'Day {i+1}')
        julian_date = row.get('julian_date', 0)

        intersection_record = {
            'day': i + 1,
            'date': date,
            'julian_date': julian_date,
            'earth_x': earth_position[0],
            'earth_y': earth_position[1],
            'earth_z': earth_position[2],
            'ray_direction_x': ray_direction[0],
            'ray_direction_y': ray_direction[1],
            'ray_direction_z': ray_direction[2],
            'intersection_count': len(intersections)
        }

        # 添加交点坐标
        for j, intersection in enumerate(intersections):
            intersection_record[f'intersection_{j+1}_x'] = intersection[0]
            intersection_record[f'intersection_{j+1}_y'] = intersection[1]
            intersection_record[f'intersection_{j+1}_z'] = intersection[2]

        intersections_data.append(intersection_record)

        if (i + 1) % 50 == 0:
            print(f"已处理 {i + 1}/{len(df)} 条射线")

    return pd.DataFrame(intersections_data)

def visualize_results(df, virtual_params, intersections_df, output_dir='planet_data'):
    """可视化结果"""
    
    print("\n正在创建3D可视化图表...")
    
    fig = plt.figure(figsize=(16, 12))
    
    # 创建3D子图
    ax1 = fig.add_subplot(221, projection='3d')
    ax2 = fig.add_subplot(222, projection='3d')
    ax3 = fig.add_subplot(223)
    ax4 = fig.add_subplot(224)
    
    # 1. 地球轨道和虚点圆
    ax1.plot(df['x_position_au'], df['y_position_au'], df['z_position_au'], 
             'b-', label='地球轨道', linewidth=2)
    ax1.scatter(0, 0, 0, color='yellow', s=200, label='太阳')
    
    # 绘制虚点
    virtual_point = virtual_params['virtual_point']
    ax1.scatter(virtual_point[0], virtual_point[1], virtual_point[2], 
                color='red', s=100, label='虚点')
    
    # 绘制虚点圆（在XY平面）
    theta = np.linspace(0, 2*np.pi, 100)
    virtual_radius = virtual_params['virtual_radius']
    circle_x = virtual_point[0] + virtual_radius * np.cos(theta)
    circle_y = virtual_point[1] + virtual_radius * np.sin(theta)
    circle_z = np.full_like(circle_x, virtual_point[2])  # Z = 0

    ax1.plot(circle_x, circle_y, circle_z, 'r--', label='虚点圆', linewidth=2)
    
    ax1.set_xlabel('X (AU)')
    ax1.set_ylabel('Y (AU)')
    ax1.set_zlabel('Z (AU)')
    ax1.set_title('地球轨道与虚点圆 (虚点圆在XY平面)')
    ax1.legend()
    
    # 2. 交点分布
    # 收集所有交点
    all_intersections_x = []
    all_intersections_y = []
    all_intersections_z = []
    
    for _, row in intersections_df.iterrows():
        for j in range(row['intersection_count']):
            if f'intersection_{j+1}_x' in row:
                all_intersections_x.append(row[f'intersection_{j+1}_x'])
                all_intersections_y.append(row[f'intersection_{j+1}_y'])
                all_intersections_z.append(row[f'intersection_{j+1}_z'])
    
    if all_intersections_x:
        ax2.scatter(all_intersections_x, all_intersections_y, all_intersections_z, 
                   c='green', s=20, alpha=0.6, label='交点')
        ax2.plot(circle_x, circle_y, circle_z, 'r--', label='虚点圆', linewidth=2)
        ax2.scatter(virtual_point[0], virtual_point[1], virtual_point[2], 
                   color='red', s=100, label='虚点')
    
    ax2.set_xlabel('X (AU)')
    ax2.set_ylabel('Y (AU)')
    ax2.set_zlabel('Z (AU)')
    ax2.set_title('射线与虚点圆交点分布')
    ax2.legend()
    
    # 3. 交点数量统计
    intersection_counts = intersections_df['intersection_count'].value_counts().sort_index()
    ax3.bar(intersection_counts.index, intersection_counts.values)
    ax3.set_xlabel('交点数量')
    ax3.set_ylabel('射线数量')
    ax3.set_title('交点数量分布')
    ax3.grid(True, alpha=0.3)
    
    # 4. 交点随时间变化
    days = intersections_df['day']
    counts = intersections_df['intersection_count']
    ax4.plot(days, counts, 'g-', linewidth=1)
    ax4.set_xlabel('天数')
    ax4.set_ylabel('交点数量')
    ax4.set_title('交点数量随时间变化')
    ax4.grid(True, alpha=0.3)
    
    plt.tight_layout()
    
    # 保存图表
    if not os.path.exists(output_dir):
        os.makedirs(output_dir)
    
    chart_file = os.path.join(output_dir, '虚点圆切面交点分析图表.png')
    plt.savefig(chart_file, dpi=300, bbox_inches='tight')
    print(f"✓ 可视化图表已保存到: {chart_file}")
    
    plt.show()

def save_results(orbital_params, virtual_params, intersections_df, output_dir='planet_data'):
    """保存计算结果"""
    
    if not os.path.exists(output_dir):
        os.makedirs(output_dir)
    
    # 保存交点数据
    intersections_file = os.path.join(output_dir, '虚点圆切面交点数据.csv')
    intersections_df.to_csv(intersections_file, index=False, encoding='utf-8-sig')
    print(f"✓ 交点数据已保存到: {intersections_file}")
    
    # 保存计算参数
    params_file = os.path.join(output_dir, '虚点圆计算参数.txt')
    with open(params_file, 'w', encoding='utf-8') as f:
        f.write("虚点圆切面交点计算参数\n")
        f.write("=" * 40 + "\n\n")
        
        f.write("轨道参数:\n")
        f.write(f"半长轴 a = {orbital_params['a']:.6f} AU\n")
        f.write(f"偏心率 e = {orbital_params['e']:.6f}\n")
        f.write(f"半短轴 b = {orbital_params['b']:.6f} AU\n")
        f.write(f"近日点距离 = {orbital_params['r_min']:.6f} AU\n")
        f.write(f"远日点距离 = {orbital_params['r_max']:.6f} AU\n\n")
        
        f.write("虚点参数:\n")
        f.write(f"虚点值 a(1-e²)/2 = {virtual_params['virtual_point_value']:.6f} AU\n")
        f.write(f"虚半径 2a - a(1-e²)/2 = {virtual_params['virtual_radius']:.6f} AU\n")
        f.write(f"虚点坐标 = {virtual_params['virtual_point']}\n\n")
        
        f.write("交点统计:\n")
        intersection_counts = intersections_df['intersection_count'].value_counts().sort_index()
        for count, freq in intersection_counts.items():
            f.write(f"{count}个交点的射线: {freq}条\n")

        total_intersections = sum(intersections_df['intersection_count'])
        f.write(f"\n总交点数: {total_intersections}\n")
        f.write(f"平均每条射线交点数: {total_intersections / len(intersections_df):.2f}\n")
    
    print(f"✓ 计算参数已保存到: {params_file}")

def main():
    """主函数"""
    print("虚点圆射线交点计算程序 (使用JPL官方标准值)")
    print("=" * 60)

    # 1. 加载地球轨道数据
    df = load_earth_orbit_data()
    if df is None:
        return

    # 2. 使用JPL官方标准轨道参数
    print("\n使用JPL官方标准轨道参数:")
    orbital_params = {
        'a': 1.00000261,  # JPL标准半长轴
        'e': 0.01671123,  # JPL标准偏心率
        'b': 1.00000261 * np.sqrt(1 - 0.01671123**2),
        'r_min': 1.00000261 * (1 - 0.01671123),
        'r_max': 1.00000261 * (1 + 0.01671123)
    }

    print(f"半长轴 a = {orbital_params['a']:.8f} AU (JPL标准值)")
    print(f"偏心率 e = {orbital_params['e']:.8f} (JPL标准值)")
    print(f"半短轴 b = {orbital_params['b']:.8f} AU")
    print(f"近日点距离 = {orbital_params['r_min']:.8f} AU")
    print(f"远日点距离 = {orbital_params['r_max']:.8f} AU")

    # 对比从数据计算的参数
    data_params = calculate_orbital_parameters(df)
    if data_params is not None:
        print(f"\n对比：从2024年数据计算的参数:")
        print(f"半长轴 a = {data_params['a']:.8f} AU (数据计算)")
        print(f"偏心率 e = {data_params['e']:.8f} (数据计算)")
        print(f"差异：Δa = {abs(orbital_params['a'] - data_params['a']):.8f} AU")
        print(f"差异：Δe = {abs(orbital_params['e'] - data_params['e']):.8f}")

    # 3. 计算虚点参数
    virtual_params = calculate_virtual_point_parameters(orbital_params)
    
    # 4. 计算射线与虚点圆的交点
    intersections_df = calculate_ray_circle_intersections(df, virtual_params)
    
    # 5. 显示统计结果
    print(f"\n计算完成！统计结果:")
    print(f"总射线数: {len(intersections_df)}")

    intersection_counts = intersections_df['intersection_count'].value_counts().sort_index()
    for count, freq in intersection_counts.items():
        print(f"{count}个交点的射线: {freq}条")

    total_intersections = sum(intersections_df['intersection_count'])
    print(f"总交点数: {total_intersections}")
    print(f"平均每条射线交点数: {total_intersections / len(intersections_df):.2f}")
    
    # 6. 保存结果
    save_results(orbital_params, virtual_params, intersections_df)
    
    # 7. 可视化
    visualize_results(df, virtual_params, intersections_df)
    
    print(f"\n🎉 虚点圆射线交点计算完成！")

if __name__ == "__main__":
    main()
