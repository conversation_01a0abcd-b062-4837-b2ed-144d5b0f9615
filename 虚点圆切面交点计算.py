#!/usr/bin/env python3
"""
基于JPL HORIZONS地球轨道数据的虚点圆切面交点计算

计算步骤：
1. 从JPL数据计算轨道参数（偏心率e，半长轴a）
2. 计算虚点值：a(1-e²)/2
3. 计算虚半径：2a - a(1-e²)/2
4. 虚点位置：(0, -a(1-e²)/2, 0)
5. 以虚点为圆心，虚半径为半径做圆
6. 以太阳为顶点，通过每日地球坐标做切面
7. 求切面与虚点圆的交点
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from mpl_toolkits.mplot3d import Axes3D
import os

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

def load_earth_orbit_data(file_path='planet_data/地球_2024_1天_处理后数据.csv'):
    """加载地球轨道数据"""
    
    if not os.path.exists(file_path):
        print(f"❌ 数据文件不存在: {file_path}")
        print("请先运行数据处理脚本生成轨道数据")
        return None
    
    try:
        df = pd.read_csv(file_path)
        print(f"✓ 成功加载轨道数据，共 {len(df)} 个数据点")
        return df
    except Exception as e:
        print(f"❌ 加载数据失败: {e}")
        return None

def calculate_orbital_parameters(df):
    """从轨道数据计算轨道参数"""
    
    if df is None:
        return None
    
    # 计算距离（如果没有distance_au列）
    if 'distance_au' not in df.columns:
        df['distance_au'] = np.sqrt(
            df['x_position_au']**2 + 
            df['y_position_au']**2 + 
            df['z_position_au']**2
        )
    
    # 计算轨道参数
    r_min = df['distance_au'].min()  # 近日点距离
    r_max = df['distance_au'].max()  # 远日点距离
    
    # 半长轴 a = (r_max + r_min) / 2
    a = (r_max + r_min) / 2
    
    # 偏心率 e = (r_max - r_min) / (r_max + r_min)
    e = (r_max - r_min) / (r_max + r_min)
    
    # 半短轴 b = a * sqrt(1 - e²)
    b = a * np.sqrt(1 - e**2)
    
    print(f"\n轨道参数计算结果:")
    print(f"半长轴 a = {a:.6f} AU ({a * 149597870.7:.0f} km)")
    print(f"偏心率 e = {e:.6f}")
    print(f"半短轴 b = {b:.6f} AU ({b * 149597870.7:.0f} km)")
    print(f"近日点距离 = {r_min:.6f} AU")
    print(f"远日点距离 = {r_max:.6f} AU")
    
    return {
        'a': a,
        'e': e,
        'b': b,
        'r_min': r_min,
        'r_max': r_max
    }

def calculate_virtual_point_parameters(orbital_params):
    """计算虚点相关参数"""
    
    a = orbital_params['a']
    e = orbital_params['e']
    
    # 虚点值：a(1-e²)/2
    virtual_point_value = a * (1 - e**2) / 2
    
    # 虚半径：2a - a(1-e²)/2
    virtual_radius = 2 * a - virtual_point_value
    
    # 虚点坐标：(0, -a(1-e²)/2, 0)
    virtual_point = np.array([0, -virtual_point_value, 0])
    
    print(f"\n虚点参数计算结果:")
    print(f"虚点值 a(1-e²)/2 = {virtual_point_value:.6f} AU ({virtual_point_value * 149597870.7:.0f} km)")
    print(f"虚半径 2a - a(1-e²)/2 = {virtual_radius:.6f} AU ({virtual_radius * 149597870.7:.0f} km)")
    print(f"虚点坐标 = ({virtual_point[0]:.6f}, {virtual_point[1]:.6f}, {virtual_point[2]:.6f}) AU")
    
    return {
        'virtual_point_value': virtual_point_value,
        'virtual_radius': virtual_radius,
        'virtual_point': virtual_point
    }

def calculate_plane_circle_intersection(plane_normal, plane_point, circle_center, circle_radius, circle_normal):
    """
    计算平面与圆的交点
    
    参数:
    plane_normal: 平面法向量
    plane_point: 平面上一点
    circle_center: 圆心
    circle_radius: 圆半径
    circle_normal: 圆所在平面的法向量
    
    返回:
    交点列表
    """
    
    # 标准化法向量
    plane_normal = plane_normal / np.linalg.norm(plane_normal)
    circle_normal = circle_normal / np.linalg.norm(circle_normal)
    
    # 计算平面到圆心的距离
    d = np.dot(plane_normal, circle_center - plane_point)
    
    # 如果距离大于圆半径，无交点
    if abs(d) > circle_radius:
        return []
    
    # 计算交线（平面与圆所在平面的交线）
    line_direction = np.cross(plane_normal, circle_normal)
    
    # 如果平面平行，无交点或重合
    if np.linalg.norm(line_direction) < 1e-10:
        if abs(d) < 1e-10:  # 平面重合
            # 返回整个圆（这里简化为圆上的一些点）
            theta = np.linspace(0, 2*np.pi, 100)
            # 需要在圆所在平面内构造两个正交向量
            if abs(circle_normal[2]) < 0.9:
                u = np.cross(circle_normal, np.array([0, 0, 1]))
            else:
                u = np.cross(circle_normal, np.array([1, 0, 0]))
            u = u / np.linalg.norm(u)
            v = np.cross(circle_normal, u)
            
            points = []
            for t in theta:
                point = circle_center + circle_radius * (np.cos(t) * u + np.sin(t) * v)
                points.append(point)
            return points
        else:
            return []  # 平行但不重合
    
    line_direction = line_direction / np.linalg.norm(line_direction)
    
    # 在圆所在平面内，找到交线上离圆心最近的点
    # 这个点是圆心在交线上的投影
    
    # 圆心到平面的投影点
    projection_on_plane = circle_center - d * plane_normal
    
    # 这个投影点到交线的距离
    to_projection = projection_on_plane - circle_center
    distance_to_line = np.linalg.norm(to_projection - np.dot(to_projection, line_direction) * line_direction)
    
    # 如果距离大于圆半径，无交点
    if distance_to_line > circle_radius:
        return []
    
    # 计算交点
    # 在交线上找到离圆心最近的点
    closest_point_on_line = circle_center + np.dot(to_projection, line_direction) * line_direction
    
    # 从这个点到圆的交点
    if distance_to_line < circle_radius:
        # 有两个交点
        chord_half_length = np.sqrt(circle_radius**2 - distance_to_line**2)
        intersection1 = closest_point_on_line + chord_half_length * line_direction
        intersection2 = closest_point_on_line - chord_half_length * line_direction
        return [intersection1, intersection2]
    else:
        # 相切，一个交点
        return [closest_point_on_line]

def calculate_cutting_planes_intersections(df, virtual_params):
    """计算切面与虚点圆的交点"""
    
    virtual_point = virtual_params['virtual_point']
    virtual_radius = virtual_params['virtual_radius']
    
    # 太阳位置（原点）
    sun_position = np.array([0, 0, 0])
    
    # 虚点圆在XZ平面内（Y = -virtual_point_value）
    circle_center = virtual_point
    circle_normal = np.array([0, 1, 0])  # Y轴方向
    
    intersections_data = []
    
    print(f"\n开始计算 {len(df)} 个切面与虚点圆的交点...")
    
    for i, row in df.iterrows():
        # 地球位置
        earth_position = np.array([
            row['x_position_au'],
            row['y_position_au'],
            row['z_position_au']
        ])
        
        # 切面法向量：从太阳到地球的向量
        plane_normal = earth_position - sun_position
        plane_normal = plane_normal / np.linalg.norm(plane_normal)
        
        # 切面通过地球位置
        plane_point = earth_position
        
        # 计算交点
        intersections = calculate_plane_circle_intersection(
            plane_normal, plane_point, circle_center, virtual_radius, circle_normal
        )
        
        # 记录结果
        date = row.get('date', f'Day {i+1}')
        julian_date = row.get('julian_date', 0)
        
        intersection_record = {
            'day': i + 1,
            'date': date,
            'julian_date': julian_date,
            'earth_x': earth_position[0],
            'earth_y': earth_position[1],
            'earth_z': earth_position[2],
            'plane_normal_x': plane_normal[0],
            'plane_normal_y': plane_normal[1],
            'plane_normal_z': plane_normal[2],
            'intersection_count': len(intersections)
        }
        
        # 添加交点坐标
        for j, intersection in enumerate(intersections):
            intersection_record[f'intersection_{j+1}_x'] = intersection[0]
            intersection_record[f'intersection_{j+1}_y'] = intersection[1]
            intersection_record[f'intersection_{j+1}_z'] = intersection[2]
        
        intersections_data.append(intersection_record)
        
        if (i + 1) % 50 == 0:
            print(f"已处理 {i + 1}/{len(df)} 个切面")
    
    return pd.DataFrame(intersections_data)

def visualize_results(df, orbital_params, virtual_params, intersections_df, output_dir='planet_data'):
    """可视化结果"""
    
    print("\n正在创建3D可视化图表...")
    
    fig = plt.figure(figsize=(16, 12))
    
    # 创建3D子图
    ax1 = fig.add_subplot(221, projection='3d')
    ax2 = fig.add_subplot(222, projection='3d')
    ax3 = fig.add_subplot(223)
    ax4 = fig.add_subplot(224)
    
    # 1. 地球轨道和虚点圆
    ax1.plot(df['x_position_au'], df['y_position_au'], df['z_position_au'], 
             'b-', label='地球轨道', linewidth=2)
    ax1.scatter(0, 0, 0, color='yellow', s=200, label='太阳')
    
    # 绘制虚点
    virtual_point = virtual_params['virtual_point']
    ax1.scatter(virtual_point[0], virtual_point[1], virtual_point[2], 
                color='red', s=100, label='虚点')
    
    # 绘制虚点圆（在XZ平面）
    theta = np.linspace(0, 2*np.pi, 100)
    virtual_radius = virtual_params['virtual_radius']
    circle_x = virtual_point[0] + virtual_radius * np.cos(theta)
    circle_z = virtual_point[2] + virtual_radius * np.sin(theta)
    circle_y = np.full_like(circle_x, virtual_point[1])
    
    ax1.plot(circle_x, circle_y, circle_z, 'r--', label='虚点圆', linewidth=2)
    
    ax1.set_xlabel('X (AU)')
    ax1.set_ylabel('Y (AU)')
    ax1.set_zlabel('Z (AU)')
    ax1.set_title('地球轨道与虚点圆')
    ax1.legend()
    
    # 2. 交点分布
    # 收集所有交点
    all_intersections_x = []
    all_intersections_y = []
    all_intersections_z = []
    
    for _, row in intersections_df.iterrows():
        for j in range(row['intersection_count']):
            if f'intersection_{j+1}_x' in row:
                all_intersections_x.append(row[f'intersection_{j+1}_x'])
                all_intersections_y.append(row[f'intersection_{j+1}_y'])
                all_intersections_z.append(row[f'intersection_{j+1}_z'])
    
    if all_intersections_x:
        ax2.scatter(all_intersections_x, all_intersections_y, all_intersections_z, 
                   c='green', s=20, alpha=0.6, label='交点')
        ax2.plot(circle_x, circle_y, circle_z, 'r--', label='虚点圆', linewidth=2)
        ax2.scatter(virtual_point[0], virtual_point[1], virtual_point[2], 
                   color='red', s=100, label='虚点')
    
    ax2.set_xlabel('X (AU)')
    ax2.set_ylabel('Y (AU)')
    ax2.set_zlabel('Z (AU)')
    ax2.set_title('切面与虚点圆交点分布')
    ax2.legend()
    
    # 3. 交点数量统计
    intersection_counts = intersections_df['intersection_count'].value_counts().sort_index()
    ax3.bar(intersection_counts.index, intersection_counts.values)
    ax3.set_xlabel('交点数量')
    ax3.set_ylabel('切面数量')
    ax3.set_title('交点数量分布')
    ax3.grid(True, alpha=0.3)
    
    # 4. 交点随时间变化
    days = intersections_df['day']
    counts = intersections_df['intersection_count']
    ax4.plot(days, counts, 'g-', linewidth=1)
    ax4.set_xlabel('天数')
    ax4.set_ylabel('交点数量')
    ax4.set_title('交点数量随时间变化')
    ax4.grid(True, alpha=0.3)
    
    plt.tight_layout()
    
    # 保存图表
    if not os.path.exists(output_dir):
        os.makedirs(output_dir)
    
    chart_file = os.path.join(output_dir, '虚点圆切面交点分析图表.png')
    plt.savefig(chart_file, dpi=300, bbox_inches='tight')
    print(f"✓ 可视化图表已保存到: {chart_file}")
    
    plt.show()

def save_results(orbital_params, virtual_params, intersections_df, output_dir='planet_data'):
    """保存计算结果"""
    
    if not os.path.exists(output_dir):
        os.makedirs(output_dir)
    
    # 保存交点数据
    intersections_file = os.path.join(output_dir, '虚点圆切面交点数据.csv')
    intersections_df.to_csv(intersections_file, index=False, encoding='utf-8-sig')
    print(f"✓ 交点数据已保存到: {intersections_file}")
    
    # 保存计算参数
    params_file = os.path.join(output_dir, '虚点圆计算参数.txt')
    with open(params_file, 'w', encoding='utf-8') as f:
        f.write("虚点圆切面交点计算参数\n")
        f.write("=" * 40 + "\n\n")
        
        f.write("轨道参数:\n")
        f.write(f"半长轴 a = {orbital_params['a']:.6f} AU\n")
        f.write(f"偏心率 e = {orbital_params['e']:.6f}\n")
        f.write(f"半短轴 b = {orbital_params['b']:.6f} AU\n")
        f.write(f"近日点距离 = {orbital_params['r_min']:.6f} AU\n")
        f.write(f"远日点距离 = {orbital_params['r_max']:.6f} AU\n\n")
        
        f.write("虚点参数:\n")
        f.write(f"虚点值 a(1-e²)/2 = {virtual_params['virtual_point_value']:.6f} AU\n")
        f.write(f"虚半径 2a - a(1-e²)/2 = {virtual_params['virtual_radius']:.6f} AU\n")
        f.write(f"虚点坐标 = {virtual_params['virtual_point']}\n\n")
        
        f.write("交点统计:\n")
        intersection_counts = intersections_df['intersection_count'].value_counts().sort_index()
        for count, freq in intersection_counts.items():
            f.write(f"{count}个交点的切面: {freq}个\n")
        
        total_intersections = sum(intersections_df['intersection_count'])
        f.write(f"\n总交点数: {total_intersections}\n")
        f.write(f"平均每个切面交点数: {total_intersections / len(intersections_df):.2f}\n")
    
    print(f"✓ 计算参数已保存到: {params_file}")

def main():
    """主函数"""
    print("虚点圆切面交点计算程序")
    print("=" * 50)
    
    # 1. 加载地球轨道数据
    df = load_earth_orbit_data()
    if df is None:
        return
    
    # 2. 计算轨道参数
    orbital_params = calculate_orbital_parameters(df)
    if orbital_params is None:
        return
    
    # 3. 计算虚点参数
    virtual_params = calculate_virtual_point_parameters(orbital_params)
    
    # 4. 计算切面与虚点圆的交点
    intersections_df = calculate_cutting_planes_intersections(df, virtual_params)
    
    # 5. 显示统计结果
    print(f"\n计算完成！统计结果:")
    print(f"总切面数: {len(intersections_df)}")
    
    intersection_counts = intersections_df['intersection_count'].value_counts().sort_index()
    for count, freq in intersection_counts.items():
        print(f"{count}个交点的切面: {freq}个")
    
    total_intersections = sum(intersections_df['intersection_count'])
    print(f"总交点数: {total_intersections}")
    print(f"平均每个切面交点数: {total_intersections / len(intersections_df):.2f}")
    
    # 6. 保存结果
    save_results(orbital_params, virtual_params, intersections_df)
    
    # 7. 可视化
    visualize_results(df, orbital_params, virtual_params, intersections_df)
    
    print(f"\n🎉 虚点圆切面交点计算完成！")

if __name__ == "__main__":
    main()
