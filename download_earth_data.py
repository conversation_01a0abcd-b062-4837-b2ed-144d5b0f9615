#!/usr/bin/env python3
"""
专门下载地球在太阳系中位置数据的脚本
使用JPL HORIZONS系统获取地球轨道数据
"""

import os
import pandas as pd
import numpy as np
import ssl
import urllib3
import warnings
from datetime import datetime
from astroquery.jplhorizons import Horizons

# 处理SSL和警告问题
warnings.filterwarnings('ignore', category=UserWarning)
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)
ssl._create_default_https_context = ssl._create_unverified_context

def download_earth_position_data(year=2024, output_dir='planet_data'):
    """
    下载地球在指定年份的位置数据
    
    参数:
    year: 年份，默认2024
    output_dir: 输出目录
    
    返回:
    pandas.DataFrame: 地球位置数据
    """
    
    # 创建输出目录
    if not os.path.exists(output_dir):
        os.makedirs(output_dir)
        print(f"创建输出目录: {output_dir}")
    
    print(f"开始下载地球在{year}年的位置数据...")
    print("=" * 50)
    
    # 地球的JPL HORIZONS ID
    earth_id = '399'
    
    # 设置时间范围
    start_time = f"{year}-01-01"
    end_time = f"{year}-12-31"
    
    print(f"天体: 地球 (ID: {earth_id})")
    print(f"时间范围: {start_time} 到 {end_time}")
    print(f"参考系: 日心坐标系 (@sun)")
    print(f"步长: 1天")
    
    try:
        print("\n正在连接JPL HORIZONS系统...")
        
        # 创建Horizons查询对象
        obj = Horizons(
            id=earth_id,
            location='@sun',  # 日心坐标系
            epochs={'start': start_time, 'stop': end_time, 'step': '1d'}
        )
        
        print("正在获取矢量数据...")
        vectors = obj.vectors()
        
        print(f"✓ 成功获取数据！共 {len(vectors)} 个数据点")
        
        # 转换为DataFrame
        df = vectors.to_pandas()
        
        # 选择和重命名关键列
        columns_mapping = {
            'datetime_jd': 'julian_date',
            'datetime_str': 'date',
            'x': 'x_position_au',
            'y': 'y_position_au', 
            'z': 'z_position_au',
            'vx': 'x_velocity_au_day',
            'vy': 'y_velocity_au_day',
            'vz': 'z_velocity_au_day',
            'range': 'distance_au',
            'range_rate': 'range_rate_au_day'
        }
        
        # 选择可用的列并重命名
        available_columns = [col for col in columns_mapping.keys() if col in df.columns]
        df_clean = df[available_columns].copy()
        df_clean.rename(columns=columns_mapping, inplace=True)
        
        # 计算轨道速度（km/s）
        if all(col in df_clean.columns for col in ['x_velocity_au_day', 'y_velocity_au_day', 'z_velocity_au_day']):
            # 1 AU/day = 1731.46 km/s
            au_day_to_km_s = 1731.46
            df_clean['speed_km_s'] = np.sqrt(
                df_clean['x_velocity_au_day']**2 + 
                df_clean['y_velocity_au_day']**2 + 
                df_clean['z_velocity_au_day']**2
            ) * au_day_to_km_s
        
        # 计算距离太阳的距离（如果没有range列）
        if 'distance_au' not in df_clean.columns:
            df_clean['distance_au'] = np.sqrt(
                df_clean['x_position_au']**2 + 
                df_clean['y_position_au']**2 + 
                df_clean['z_position_au']**2
            )
        
        # 保存数据
        filename = f"地球_{year}_轨道数据.csv"
        filepath = os.path.join(output_dir, filename)
        df_clean.to_csv(filepath, index=False, encoding='utf-8-sig')
        
        print(f"\n✓ 数据已保存到: {filepath}")
        
        # 显示数据摘要
        print(f"\n地球轨道数据摘要 ({year}年):")
        print("=" * 40)
        print(f"数据点数: {len(df_clean)}")
        print(f"数据列: {list(df_clean.columns)}")
        
        if 'distance_au' in df_clean.columns:
            avg_dist = df_clean['distance_au'].mean()
            min_dist = df_clean['distance_au'].min()
            max_dist = df_clean['distance_au'].max()
            print(f"\n轨道参数:")
            print(f"平均日心距离: {avg_dist:.6f} AU ({avg_dist * 149597870.7:.0f} km)")
            print(f"近日点距离: {min_dist:.6f} AU ({min_dist * 149597870.7:.0f} km)")
            print(f"远日点距离: {max_dist:.6f} AU ({max_dist * 149597870.7:.0f} km)")
            print(f"轨道偏心率: {(max_dist - min_dist) / (max_dist + min_dist):.6f}")
        
        if 'speed_km_s' in df_clean.columns:
            avg_speed = df_clean['speed_km_s'].mean()
            max_speed = df_clean['speed_km_s'].max()
            min_speed = df_clean['speed_km_s'].min()
            print(f"\n速度参数:")
            print(f"平均轨道速度: {avg_speed:.2f} km/s")
            print(f"最高轨道速度: {max_speed:.2f} km/s (近日点)")
            print(f"最低轨道速度: {min_speed:.2f} km/s (远日点)")
        
        # 显示前几行数据
        print(f"\n前5天的数据预览:")
        print(df_clean.head().to_string())
        
        return df_clean
        
    except Exception as e:
        print(f"❌ 下载失败: {e}")
        print("\n可能的解决方案:")
        print("1. 检查网络连接")
        print("2. 确认代理设置正确")
        print("3. 稍后重试")
        return None

def main():
    """主函数"""
    print("地球轨道数据下载器")
    print("使用NASA JPL HORIZONS系统")
    print("=" * 50)
    
    # 下载2024年地球数据
    earth_data = download_earth_position_data(2024)
    
    if earth_data is not None:
        print("\n🎉 地球轨道数据下载成功！")
        print("\n数据文件位置: planet_data/地球_2024_轨道数据.csv")
        print("\n数据包含:")
        print("- 每日的三维位置坐标 (x, y, z)")
        print("- 每日的三维速度矢量 (vx, vy, vz)")
        print("- 日心距离")
        print("- 轨道速度")
        print("- 儒略日期和日期字符串")
    else:
        print("\n❌ 数据下载失败")
        print("请检查网络连接和代理设置")

if __name__ == "__main__":
    main()
