#!/usr/bin/env python3
"""
JPL HORIZONS系统测试脚本
测试是否能正常连接和获取数据
"""

import sys
import os

def test_imports():
    """测试必要的库是否能正常导入"""
    print("测试库导入...")
    
    try:
        import numpy as np
        print("✓ numpy 导入成功")
    except ImportError as e:
        print(f"✗ numpy 导入失败: {e}")
        return False
    
    try:
        import pandas as pd
        print("✓ pandas 导入成功")
    except ImportError as e:
        print(f"✗ pandas 导入失败: {e}")
        return False
    
    try:
        from astroquery.jplhorizons import Horizons
        print("✓ astroquery.jplhorizons 导入成功")
    except ImportError as e:
        print(f"✗ astroquery.jplhorizons 导入失败: {e}")
        return False
    
    try:
        from astropy.time import Time
        print("✓ astropy.time 导入成功")
    except ImportError as e:
        print(f"✗ astropy.time 导入失败: {e}")
        return False
    
    return True

def test_simple_query():
    """测试简单的JPL HORIZONS查询"""
    print("\n测试JPL HORIZONS查询...")
    
    try:
        from astroquery.jplhorizons import Horizons
        
        # 创建一个简单的查询（地球，获取3天的数据）
        print("创建查询对象...")
        obj = Horizons(
            id='399',  # 地球
            location='@sun',  # 日心坐标系
            epochs={'start': '2023-01-01', 'stop': '2023-01-03', 'step': '1d'}
        )
        
        print("正在查询JPL HORIZONS系统...")
        vectors = obj.vectors()
        
        print(f"✓ 查询成功！获取到 {len(vectors)} 条记录")
        
        # 显示数据结构
        print("\n数据列:")
        for col in vectors.colnames:
            print(f"  - {col}")
        
        # 显示前几行数据
        print(f"\n前3行数据:")
        df = vectors.to_pandas()
        print(df.head(3))
        
        return True
        
    except Exception as e:
        print(f"✗ 查询失败: {e}")
        return False

def test_data_fetcher():
    """测试我们的数据获取器"""
    print("\n测试数据获取器...")
    
    try:
        from jpl_horizons_fetcher import JPLHorizonsDataFetcher
        
        print("创建数据获取器...")
        fetcher = JPLHorizonsDataFetcher()
        
        print("测试获取地球3天的数据...")
        # 修改获取器以支持自定义日期范围（用于测试）
        from astroquery.jplhorizons import Horizons
        
        obj = Horizons(
            id='399',  # 地球
            location='@sun',
            epochs={'start': '2023-01-01', 'stop': '2023-01-03', 'step': '1d'}
        )
        
        vectors = obj.vectors()
        df = vectors.to_pandas()
        
        print(f"✓ 成功获取 {len(df)} 天的数据")
        
        # 测试保存功能
        print("测试保存CSV功能...")
        if not os.path.exists('planet_data'):
            os.makedirs('planet_data')
        
        test_file = 'planet_data/test_earth_data.csv'
        df.to_csv(test_file, index=False)
        print(f"✓ 测试数据已保存到: {test_file}")
        
        return True
        
    except Exception as e:
        print(f"✗ 数据获取器测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("JPL HORIZONS系统测试")
    print("=" * 40)
    
    # 测试库导入
    if not test_imports():
        print("\n❌ 库导入测试失败，请检查依赖安装")
        return False
    
    # 测试简单查询
    if not test_simple_query():
        print("\n❌ JPL HORIZONS查询测试失败，请检查网络连接")
        return False
    
    # 测试数据获取器
    if not test_data_fetcher():
        print("\n❌ 数据获取器测试失败")
        return False
    
    print("\n" + "=" * 40)
    print("🎉 所有测试通过！系统可以正常使用")
    print("\n下一步可以运行:")
    print("  python jpl_horizons_fetcher.py")
    print("或者:")
    print("  python example_usage.py")
    
    return True

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
