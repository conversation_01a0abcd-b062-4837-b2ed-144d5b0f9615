import numpy as np
import matplotlib.pyplot as plt
from matplotlib.animation import FuncAnimation
from matplotlib.patches import Circle, Ellipse
plt.rcParams['font.sans-serif'] = ['SimHei']  # 用来正常显示中文标签
plt.rcParams['axes.unicode_minus'] = False  # 用来正常显示负号

class GeometricTrace:
    def __init__(self):
        # 设置参数
        self.inner_radius = 0.5  # 内圈圆半径
        self.a = 1.0  # 椭圆半长轴
        self.e = 0.5  # 椭圆偏心率
        self.b = self.a * np.sqrt(1 - self.e**2)  # 椭圆半短轴
        
        # 创建图形
        self.fig, self.ax = plt.subplots(figsize=(12, 10))
        self.ax.set_aspect('equal')
        self.ax.grid(True)
        self.ax.set_xlim(-2.5, 2.5)
        self.ax.set_ylim(-2.5, 2.5)
        
        # 添加标题和说明
        self.ax.set_title('几何轨迹演示', fontsize=12)
        
        # 添加方程说明
        equations = [
            '内圆方程：x² + y² = 0.5²',
            '椭圆方程：(x-%.2f)²/%.2f² + y²/%.2f² = 1' % (self.a*self.e/2, self.a, self.b),
            'P点性质：NP = AN，其中N是射线OA与椭圆的交点'
        ]
        equation_text = '\n'.join(equations)
        self.ax.text(-2.3, 2.0, equation_text, 
                    bbox=dict(facecolor='white', alpha=0.8, edgecolor='gray'),
                    fontsize=10)
        
        # 添加构造说明
        construction_steps = [
            '构造步骤：',
            '1. A点在内圆上运动',
            '2. N点是射线OA与椭圆的交点',
            '3. P点是延长AN等长得到的点',
            '4. 椭圆性质：ON·NF = 1'
        ]
        steps_text = '\n'.join(construction_steps)
        self.ax.text(-2.3, -0.5, steps_text,
                    bbox=dict(facecolor='white', alpha=0.8, edgecolor='gray'),
                    fontsize=10)
        
        # 绘制内圈圆
        inner_circle = Circle((0, 0), self.inner_radius, fill=False, color='blue', label='内圆')
        self.ax.add_patch(inner_circle)
        
        # 绘制椭圆
        ellipse = Ellipse((self.a*self.e/2, 0), 2*self.a, 2*self.b, fill=False, color='red', label='椭圆')
        self.ax.add_patch(ellipse)
        
        # 绘制外圈圆（P点轨迹）
        outer_circle = Circle((0, 0), 2.0, fill=False, color='green', linestyle='--', label='P点轨迹圆')
        self.ax.add_patch(outer_circle)
        
        # 绘制焦点
        self.ax.plot(0, 0, 'k.', label='焦点O')
        self.ax.plot(self.a*self.e, 0, 'k.', label='焦点F')
        
        # 初始化动态点
        self.point_A, = self.ax.plot([], [], 'bo', label='点A')
        self.point_N, = self.ax.plot([], [], 'ro', label='点N')
        self.point_P, = self.ax.plot([], [], 'go', label='点P')
        self.line_OA, = self.ax.plot([], [], 'k--', alpha=0.3)
        self.line_NP, = self.ax.plot([], [], 'g--', alpha=0.3)
        
        self.ax.legend()
        
    def calculate_points(self, theta):
        # 计算A点坐标
        A_x = self.inner_radius * np.cos(theta)
        A_y = self.inner_radius * np.sin(theta)
        
        # 计算射线OA与椭圆的交点N
        if abs(A_x) < 1e-10:  # 防止除以零
            k = np.inf
            N_x = 0
            N_y = self.b if A_y > 0 else -self.b
        else:
            k = A_y / A_x  # tan(θ)
            # 计算二次方程系数
            c = self.a * self.e / 2  # 椭圆中心到焦点的距离
            
            # (b² + a²k²)x² - 2b²cx + (b²c² - a²b²) = 0
            A = self.b**2 + self.a**2 * k**2
            B = -2 * self.b**2 * c
            C = self.b**2 * c**2 - self.a**2 * self.b**2
            
            # 求解一元二次方程
            discriminant = B**2 - 4*A*C
            if discriminant < 0:
                discriminant = 0
            
            x1 = (-B + np.sqrt(discriminant)) / (2*A)
            x2 = (-B - np.sqrt(discriminant)) / (2*A)
            
            # 选择与射线OA方向一致的解
            if A_x > 0:
                N_x = max(x1, x2)
            else:
                N_x = min(x1, x2)
            N_y = k * N_x
        
        # 计算AN的向量
        AN_x = N_x - A_x
        AN_y = N_y - A_y
        
        # P点坐标：延长AN得到
        P_x = N_x + AN_x
        P_y = N_y + AN_y
        
        return (A_x, A_y), (N_x, N_y), (P_x, P_y)
    
    def update(self, frame):
        theta = frame * 2 * np.pi / 360  # 将帧转换为角度
        (A_x, A_y), (N_x, N_y), (P_x, P_y) = self.calculate_points(theta)
        
        # 更新点的位置
        self.point_A.set_data([A_x], [A_y])
        self.point_N.set_data([N_x], [N_y])
        self.point_P.set_data([P_x], [P_y])
        
        # 更新线段
        self.line_OA.set_data([0, N_x], [0, N_y])
        self.line_NP.set_data([N_x, P_x], [N_y, P_y])
        
        return self.point_A, self.point_N, self.point_P, self.line_OA, self.line_NP

    def animate(self):
        anim = FuncAnimation(self.fig, self.update, frames=360,
                           interval=50, blit=True)
        plt.show()

if __name__ == "__main__":
    trace = GeometricTrace()
    trace.animate()
